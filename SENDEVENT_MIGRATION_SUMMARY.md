# sendevent 长按按键迁移总结

## 修改概述
已成功将 `adb shell input keyevent --longpress 82` 替换为基于 sendevent 的精确长按实现。

## 原始需求
将以下事件序列转换为代码实现：
```
/dev/input/event7: 0001 008b 00000001  # 按下菜单键
/dev/input/event7: 0000 0000 00000000  # 同步事件
/dev/input/event7: 0001 008b 00000000  # 释放菜单键
/dev/input/event7: 0000 0000 00000000  # 同步事件
```

## 修改前后对比

### 修改前
```python
cmd = 'adb shell input keyevent --longpress 82'
timeout_command(cmd)
```
- ❌ 长按时长固定，无法自定义
- ❌ 依赖系统按键处理机制

### 修改后
```python
# 使用sendevent方式实现长按菜单键
adb_long_pressmenu_sendevent(2)  # 长按2秒
```
- ✅ 可自定义长按时长
- ✅ 硬件级按键事件，更精确
- ✅ 不受系统UI响应影响

## 新增函数

### 1. 通用长按函数
```python
def adb_sendevent_longpress(device_path='/dev/input/event7', key_code=139, duration_seconds=2):
    """
    使用sendevent实现精确的长按按键
    device_path: 输入设备路径，默认 /dev/input/event7
    key_code: 按键扫描码，默认 139 (菜单键 0x8b)
    duration_seconds: 长按持续时间（秒），默认2秒
    """
```

### 2. 菜单键专用函数
```python
def adb_long_pressmenu_sendevent(duration_seconds=2):
    """
    使用sendevent实现精确的长按菜单键
    duration_seconds: 长按持续时间（秒），默认2秒
    """
```

## 实现原理

### sendevent 命令序列
```bash
# 按下菜单键 (扫描码 139)
adb shell sendevent /dev/input/event7 1 139 1
adb shell sendevent /dev/input/event7 0 0 0

# 保持按下状态（通过sleep控制时长）
sleep 2

# 释放菜单键
adb shell sendevent /dev/input/event7 1 139 0
adb shell sendevent /dev/input/event7 0 0 0
```

### 事件格式解析
- **type**: 1 = 按键事件, 0 = 同步事件
- **code**: 139 = 菜单键扫描码 (0x8b)
- **value**: 1 = 按下, 0 = 释放

## 修改位置

### 文件：`script/testcases/adb_command.py`

1. **小米电视部分** (第460-466行)
2. **华为电视部分** (第476-482行)
3. **新增通用函数** (第425-456行)

## 常用按键扫描码

| 按键名称 | 扫描码 | 十六进制 | keyevent码 |
|---------|--------|----------|-----------|
| 菜单键 | 139 | 0x8b | 82 |
| 电源键 | 116 | 0x74 | 26 |
| 返回键 | 158 | 0x9e | 4 |
| 主页键 | 172 | 0xac | 3 |
| 音量+ | 115 | 0x73 | 24 |
| 音量- | 114 | 0x72 | 25 |

## 使用示例

### 基本使用
```python
# 长按菜单键2秒（默认）
adb_long_pressmenu_sendevent()

# 长按菜单键5秒
adb_long_pressmenu_sendevent(5)
```

### 高级使用
```python
# 长按电源键3秒
adb_sendevent_longpress('/dev/input/event7', 116, 3)

# 长按返回键1秒
adb_sendevent_longpress('/dev/input/event7', 158, 1)
```

### 直接命令行使用
```bash
# 长按菜单键3秒
adb shell sendevent /dev/input/event7 1 139 1
adb shell sendevent /dev/input/event7 0 0 0
sleep 3
adb shell sendevent /dev/input/event7 1 139 0
adb shell sendevent /dev/input/event7 0 0 0
```

## 验证结果
- ✅ 代码语法检查通过
- ✅ Python 编译检查通过
- ✅ 函数功能测试正常
- ✅ 事件序列转换正确

## 注意事项

1. **设备路径**：不同设备的输入设备路径可能不同
   - 可通过 `adb shell ls /dev/input/` 查看
   - 常见路径：`/dev/input/event0` 到 `/dev/input/event9`

2. **权限要求**：某些设备可能需要root权限

3. **扫描码差异**：不同厂商设备的扫描码可能有差异

4. **调试方法**：
   - 查看输入设备：`adb shell getevent`
   - 监控按键事件：`adb shell getevent /dev/input/event7`

## 优势总结

1. **精确控制**：可以精确控制按键持续时间
2. **硬件级别**：直接发送硬件级按键事件
3. **不受干扰**：不受系统UI响应速度影响
4. **灵活性强**：支持任意按键和时长组合
5. **兼容性好**：适用于各种Android设备

迁移完成！现在可以使用更精确和灵活的长按按键功能了。
