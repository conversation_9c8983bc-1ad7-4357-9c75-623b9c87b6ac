#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
sendevent 长按按键测试示例
"""

import sys
import os
import time

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'script', 'testcases'))

def demo_sendevent_commands():
    """演示sendevent命令的生成"""
    print("=== sendevent 长按按键命令演示 ===\n")
    
    # 根据你提供的事件序列生成的命令
    print("1. 长按菜单键2秒的命令序列:")
    print("   # 按下菜单键 (code 139 = 0x8b)")
    print("   adb shell sendevent /dev/input/event7 1 139 1")
    print("   adb shell sendevent /dev/input/event7 0 0 0")
    print("   # 保持按下状态2秒")
    print("   sleep 2")
    print("   # 释放菜单键")
    print("   adb shell sendevent /dev/input/event7 1 139 0")
    print("   adb shell sendevent /dev/input/event7 0 0 0")
    print()
    
    print("2. 其他常用按键的sendevent命令:")
    keys = [
        ("电源键", 116, "0x74"),
        ("返回键", 158, "0x9e"), 
        ("主页键", 172, "0xac"),
        ("音量+", 115, "0x73"),
        ("音量-", 114, "0x72"),
    ]
    
    for name, code, hex_code in keys:
        print(f"   {name} (code {code} = {hex_code}):")
        print(f"   adb shell sendevent /dev/input/event7 1 {code} 1")
        print(f"   adb shell sendevent /dev/input/event7 0 0 0")
        print(f"   sleep 2  # 长按2秒")
        print(f"   adb shell sendevent /dev/input/event7 1 {code} 0")
        print(f"   adb shell sendevent /dev/input/event7 0 0 0")
        print()

def demo_function_usage():
    """演示函数使用方法"""
    print("=== 函数使用方法演示 ===\n")
    
    try:
        from adb_command import adb_sendevent_longpress, adb_long_pressmenu_sendevent
        
        print("1. 使用专用菜单键长按函数:")
        print("   adb_long_pressmenu_sendevent(2)    # 长按菜单键2秒")
        print("   adb_long_pressmenu_sendevent(5)    # 长按菜单键5秒")
        print()
        
        print("2. 使用通用长按函数:")
        print("   # 长按菜单键3秒")
        print("   adb_sendevent_longpress('/dev/input/event7', 139, 3)")
        print()
        print("   # 长按电源键2秒")
        print("   adb_sendevent_longpress('/dev/input/event7', 116, 2)")
        print()
        print("   # 长按返回键1秒")
        print("   adb_sendevent_longpress('/dev/input/event7', 158, 1)")
        print()
        
        print("3. 实际执行示例 (注释掉以避免误操作):")
        print("   # 取消下面的注释来实际执行")
        print("   # adb_long_pressmenu_sendevent(1)  # 长按菜单键1秒")
        
    except ImportError:
        print("无法导入函数，请确保在正确的项目目录中运行")

def show_event_format_explanation():
    """显示事件格式说明"""
    print("=== sendevent 事件格式说明 ===\n")
    
    print("你提供的原始事件序列:")
    print("/dev/input/event7: 0001 008b 00000001")
    print("/dev/input/event7: 0000 0000 00000000") 
    print("/dev/input/event7: 0001 008b 00000000")
    print("/dev/input/event7: 0000 0000 00000000")
    print()
    
    print("格式解析:")
    print("设备路径: type code value")
    print("- type: 0001 = 按键事件, 0000 = 同步事件")
    print("- code: 008b = 139 (十进制) = 菜单键扫描码")
    print("- value: 00000001 = 1 (按下), 00000000 = 0 (释放)")
    print()
    
    print("转换为adb命令:")
    print("adb shell sendevent /dev/input/event7 1 139 1  # 按下")
    print("adb shell sendevent /dev/input/event7 0 0 0    # 同步")
    print("# ... 等待指定时间 ...")
    print("adb shell sendevent /dev/input/event7 1 139 0  # 释放")
    print("adb shell sendevent /dev/input/event7 0 0 0    # 同步")

if __name__ == "__main__":
    demo_sendevent_commands()
    print("\n" + "="*50 + "\n")
    demo_function_usage()
    print("\n" + "="*50 + "\n")
    show_event_format_explanation()
    
    print("\n=== 使用提示 ===")
    print("1. 确保设备已连接: adb devices")
    print("2. 查看输入设备: adb shell ls /dev/input/")
    print("3. 监控按键事件: adb shell getevent /dev/input/event7")
    print("4. 某些设备可能需要root权限")
    print("5. 设备路径可能因设备而异，请根据实际情况调整")
