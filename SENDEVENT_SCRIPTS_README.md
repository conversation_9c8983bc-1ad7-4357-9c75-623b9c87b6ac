# sendevent 长按按键模拟脚本说明

## 脚本概述

我为你创建了3个脚本来模拟和执行基于 sendevent 的长按按键过程：

### 1. `simulate_sendevent_longpress.py` - 完整交互式模拟器
- 🎮 交互式菜单操作
- 📊 详细的事件序列分析
- 🔄 批量演示不同按键
- 🛡️ 安全模式（默认不实际执行）

### 2. `simple_longpress_demo.py` - 简单演示脚本
- 🎯 直接演示完整过程
- 📋 显示命令序列
- 🐍 提供Python函数实现
- ⚖️ 方法对比说明

### 3. `execute_longpress_demo.py` - 实际执行脚本
- ⚡ 可真正执行sendevent命令
- 🔍 自动检查设备连接
- 🎮 交互式参数设置
- ⚠️ 安全确认机制

## 基于的事件序列

你提供的原始事件序列：
```
/dev/input/event7: 0001 008b 00000001  # 按下菜单键
/dev/input/event7: 0000 0000 00000000  # 同步事件
/dev/input/event7: 0001 008b 00000000  # 释放菜单键
/dev/input/event7: 0000 0000 00000000  # 同步事件
```

## 转换后的命令序列

```bash
# 1. 按下菜单键
adb shell sendevent /dev/input/event7 1 139 1
adb shell sendevent /dev/input/event7 0 0 0

# 2. 保持按下状态（通过sleep控制时长）
sleep 2

# 3. 释放菜单键
adb shell sendevent /dev/input/event7 1 139 0
adb shell sendevent /dev/input/event7 0 0 0
```

## 使用方法

### 运行演示脚本（安全）
```bash
# 简单演示
python3 simple_longpress_demo.py

# 完整交互式演示
python3 simulate_sendevent_longpress.py
```

### 实际执行（需要设备连接）
```bash
python3 execute_longpress_demo.py
```

## 脚本功能对比

| 功能 | simulate_* | simple_* | execute_* |
|------|------------|----------|-----------|
| 事件序列分析 | ✅ | ✅ | ❌ |
| 交互式菜单 | ✅ | ❌ | ✅ |
| 批量演示 | ✅ | ❌ | ❌ |
| 实际执行 | 可选 | ❌ | ✅ |
| 设备检查 | ✅ | ❌ | ✅ |
| 安全确认 | ✅ | N/A | ✅ |

## 核心实现函数

所有脚本都基于这个核心逻辑：

```python
def adb_longpress_sendevent(device_path='/dev/input/event7', key_code=139, duration_seconds=2):
    """使用sendevent实现长按按键"""
    import subprocess
    import time
    
    # 按下按键
    subprocess.run(f"adb shell sendevent {device_path} 1 {key_code} 1", shell=True)
    subprocess.run(f"adb shell sendevent {device_path} 0 0 0", shell=True)
    
    # 保持按下状态
    time.sleep(duration_seconds)
    
    # 释放按键
    subprocess.run(f"adb shell sendevent {device_path} 1 {key_code} 0", shell=True)
    subprocess.run(f"adb shell sendevent {device_path} 0 0 0", shell=True)
```

## 参数说明

### 设备路径 (device_path)
- 默认：`/dev/input/event7`
- 其他可能：`/dev/input/event0` 到 `/dev/input/event9`
- 查看方法：`adb shell ls /dev/input/`

### 按键码 (key_code)
| 按键 | 扫描码 | 十六进制 |
|------|--------|----------|
| 菜单键 | 139 | 0x8b |
| 电源键 | 116 | 0x74 |
| 返回键 | 158 | 0x9e |
| 主页键 | 172 | 0xac |

### 长按时长 (duration_seconds)
- 单位：秒
- 默认：2秒
- 可自定义任意时长

## 使用场景

### 1. 学习理解
使用 `simple_longpress_demo.py` 了解整个过程

### 2. 测试验证
使用 `simulate_sendevent_longpress.py` 进行安全测试

### 3. 实际应用
使用 `execute_longpress_demo.py` 在真实设备上执行

## 注意事项

### 设备要求
- ✅ Android设备已连接
- ✅ 已开启USB调试
- ✅ 已授权ADB调试
- ⚠️ 某些设备可能需要root权限

### 兼容性
- 📱 设备路径可能因设备而异
- 🔧 扫描码可能因厂商而异
- 🧪 建议先在测试设备上验证

### 安全性
- 🛡️ 脚本包含安全确认机制
- ⚠️ 长按电源键可能导致设备关机
- 🔍 建议先使用模拟模式测试

## 故障排除

### ADB连接问题
```bash
# 检查设备连接
adb devices

# 重启ADB服务
adb kill-server
adb start-server
```

### 权限问题
```bash
# 检查是否需要root权限
adb shell su -c "ls /dev/input/"
```

### 设备路径问题
```bash
# 查看可用输入设备
adb shell ls /dev/input/

# 监控按键事件
adb shell getevent
```

## 总结

这些脚本完整地模拟了你提供的 sendevent 事件序列，从理论分析到实际执行，提供了完整的解决方案。你可以根据需要选择合适的脚本使用。
