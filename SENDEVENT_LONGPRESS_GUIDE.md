# sendevent 长按按键实现指南

## 修改说明

### 原始代码
```python
cmd = 'adb shell input keyevent --longpress 82'
timeout_command(cmd)
```

### 修改后代码
```python
# 使用sendevent方式实现长按菜单键
adb_long_pressmenu_sendevent(2)  # 长按2秒
```

## sendevent 事件序列分析

### 你提供的事件序列
```
/dev/input/event7: 0001 008b 00000001  # 按下菜单键
/dev/input/event7: 0000 0000 00000000  # 同步事件
/dev/input/event7: 0001 008b 00000000  # 释放菜单键  
/dev/input/event7: 0000 0000 00000000  # 同步事件
```

### 事件格式解析
```
设备路径: type code value
- type: 事件类型 (0001=按键事件, 0000=同步事件)
- code: 按键扫描码 (008b=139=菜单键)
- value: 事件值 (1=按下, 0=释放)
```

### 对应的 adb 命令
```bash
# 按下菜单键
adb shell sendevent /dev/input/event7 1 139 1
adb shell sendevent /dev/input/event7 0 0 0

# 保持按下状态（通过sleep控制时长）
sleep 2

# 释放菜单键
adb shell sendevent /dev/input/event7 1 139 0
adb shell sendevent /dev/input/event7 0 0 0
```

## 新增函数说明

### 1. 通用长按函数
```python
adb_sendevent_longpress(
    device_path='/dev/input/event7',  # 输入设备路径
    key_code=139,                     # 按键扫描码
    duration_seconds=2                # 长按时长(秒)
)
```

### 2. 菜单键专用函数
```python
adb_long_pressmenu_sendevent(duration_seconds=2)
```

## 常用按键扫描码对照表

| 按键名称 | 十进制码 | 十六进制码 | keyevent码 |
|---------|---------|-----------|-----------|
| 菜单键 | 139 | 0x8b | 82 |
| 电源键 | 116 | 0x74 | 26 |
| 返回键 | 158 | 0x9e | 4 |
| 主页键 | 172 | 0xac | 3 |
| 音量+ | 115 | 0x73 | 24 |
| 音量- | 114 | 0x72 | 25 |
| 确认键 | 28 | 0x1c | 23 |

## 使用示例

### 基本使用
```python
# 长按菜单键2秒
adb_long_pressmenu_sendevent(2)

# 长按菜单键5秒
adb_long_pressmenu_sendevent(5)
```

### 高级使用
```python
# 长按电源键3秒
adb_sendevent_longpress('/dev/input/event7', 116, 3)

# 长按返回键1秒
adb_sendevent_longpress('/dev/input/event7', 158, 1)
```

### 直接命令行使用
```bash
# 长按菜单键3秒的完整命令序列
adb shell sendevent /dev/input/event7 1 139 1
adb shell sendevent /dev/input/event7 0 0 0
sleep 3
adb shell sendevent /dev/input/event7 1 139 0
adb shell sendevent /dev/input/event7 0 0 0
```

## 优势对比

### sendevent 方式优势
- ✅ 精确控制按键时长
- ✅ 真正的硬件级按键事件
- ✅ 可以模拟复杂的按键组合
- ✅ 不受系统UI响应影响

### keyevent 方式对比
- ❌ 长按时长固定，无法自定义
- ❌ 依赖系统按键处理机制
- ✅ 使用简单，兼容性好
- ✅ 不需要知道设备路径

## 注意事项

1. **设备路径**：不同设备的输入设备路径可能不同
   - 常见路径：`/dev/input/event0` 到 `/dev/input/event9`
   - 可通过 `adb shell getevent` 查看可用设备

2. **权限要求**：某些设备可能需要root权限

3. **扫描码差异**：不同厂商的设备扫描码可能有差异

4. **设备兼容性**：建议先在目标设备上测试

## 调试方法

### 查看输入设备
```bash
adb shell getevent
```

### 监控按键事件
```bash
adb shell getevent /dev/input/event7
```

### 查找正确的设备路径
```bash
adb shell ls /dev/input/
```
