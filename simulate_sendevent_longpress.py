#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟 sendevent 长按按键过程的脚本
基于你提供的事件序列：
/dev/input/event7: 0001 008b 00000001
/dev/input/event7: 0000 0000 00000000
/dev/input/event7: 0001 008b 00000000
/dev/input/event7: 0000 0000 00000000
"""

import time
import subprocess
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'script', 'testcases'))

def execute_command(cmd, description=""):
    """执行命令并显示过程"""
    print(f"📱 执行: {description}")
    print(f"   命令: {cmd}")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"   ✅ 成功")
            if result.stdout.strip():
                print(f"   输出: {result.stdout.strip()}")
        else:
            print(f"   ❌ 失败 (返回码: {result.returncode})")
            if result.stderr.strip():
                print(f"   错误: {result.stderr.strip()}")
    except subprocess.TimeoutExpired:
        print(f"   ⏰ 超时")
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    print()

def simulate_sendevent_longpress(device_path='/dev/input/event7', key_code=139, duration_seconds=2, dry_run=True):
    """
    模拟 sendevent 长按按键过程
    
    Args:
        device_path: 输入设备路径
        key_code: 按键扫描码 (139 = 0x8b = 菜单键)
        duration_seconds: 长按持续时间
        dry_run: True=仅模拟显示，False=实际执行
    """
    print("=" * 60)
    print("🎯 sendevent 长按按键模拟过程")
    print("=" * 60)
    print(f"设备路径: {device_path}")
    print(f"按键码: {key_code} (0x{key_code:02x})")
    print(f"长按时长: {duration_seconds} 秒")
    print(f"模式: {'仅模拟' if dry_run else '实际执行'}")
    print()
    
    # 步骤1: 按下按键
    print("🔽 步骤1: 按下按键")
    print("   对应事件: /dev/input/event7: 0001 008b 00000001")
    cmd1 = f'adb shell sendevent {device_path} 1 {key_code} 1'
    if not dry_run:
        execute_command(cmd1, "发送按键按下事件")
    else:
        print(f"   模拟命令: {cmd1}")
        print("   ✅ 模拟成功")
        print()
    
    # 步骤2: 同步事件
    print("🔄 步骤2: 发送同步事件")
    print("   对应事件: /dev/input/event7: 0000 0000 00000000")
    cmd2 = f'adb shell sendevent {device_path} 0 0 0'
    if not dry_run:
        execute_command(cmd2, "发送同步事件")
    else:
        print(f"   模拟命令: {cmd2}")
        print("   ✅ 模拟成功")
        print()
    
    # 步骤3: 保持按下状态
    print(f"⏱️  步骤3: 保持按下状态 {duration_seconds} 秒")
    print("   这期间按键保持按下状态...")
    for i in range(duration_seconds):
        print(f"   ⏳ {i+1}/{duration_seconds} 秒", end="", flush=True)
        time.sleep(1)
        print(" ✓")
    print()
    
    # 步骤4: 释放按键
    print("🔼 步骤4: 释放按键")
    print("   对应事件: /dev/input/event7: 0001 008b 00000000")
    cmd3 = f'adb shell sendevent {device_path} 1 {key_code} 0'
    if not dry_run:
        execute_command(cmd3, "发送按键释放事件")
    else:
        print(f"   模拟命令: {cmd3}")
        print("   ✅ 模拟成功")
        print()
    
    # 步骤5: 最终同步事件
    print("🔄 步骤5: 发送最终同步事件")
    print("   对应事件: /dev/input/event7: 0000 0000 00000000")
    cmd4 = f'adb shell sendevent {device_path} 0 0 0'
    if not dry_run:
        execute_command(cmd4, "发送最终同步事件")
    else:
        print(f"   模拟命令: {cmd4}")
        print("   ✅ 模拟成功")
        print()
    
    print("🎉 长按按键过程完成!")
    print("=" * 60)

def show_event_sequence_analysis():
    """显示事件序列分析"""
    print("\n📊 事件序列分析")
    print("=" * 40)
    print("原始事件序列:")
    events = [
        ("/dev/input/event7: 0001 008b 00000001", "按下菜单键", "type=1, code=139, value=1"),
        ("/dev/input/event7: 0000 0000 00000000", "同步事件", "type=0, code=0, value=0"),
        ("/dev/input/event7: 0001 008b 00000000", "释放菜单键", "type=1, code=139, value=0"),
        ("/dev/input/event7: 0000 0000 00000000", "同步事件", "type=0, code=0, value=0")
    ]
    
    for i, (event, desc, detail) in enumerate(events, 1):
        print(f"{i}. {event}")
        print(f"   → {desc} ({detail})")
        print()

def check_device_connection():
    """检查设备连接状态"""
    print("🔍 检查设备连接状态...")
    cmd = "adb devices"
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    
    if result.returncode == 0:
        lines = result.stdout.strip().split('\n')
        devices = [line for line in lines[1:] if line.strip() and 'device' in line]
        
        if devices:
            print(f"✅ 发现 {len(devices)} 个设备:")
            for device in devices:
                print(f"   📱 {device}")
            return True
        else:
            print("❌ 未发现连接的设备")
            return False
    else:
        print(f"❌ adb命令执行失败: {result.stderr}")
        return False

def interactive_demo():
    """交互式演示"""
    print("\n🎮 交互式演示")
    print("=" * 40)
    
    # 检查设备连接
    if not check_device_connection():
        print("\n⚠️  警告: 未检测到设备连接，将仅进行模拟演示")
        dry_run = True
    else:
        choice = input("\n是否实际执行命令? (y/N): ").lower().strip()
        dry_run = choice != 'y'
    
    print()
    
    # 获取参数
    try:
        duration = int(input("请输入长按时长(秒，默认2): ") or "2")
    except ValueError:
        duration = 2
    
    device_path = input("请输入设备路径(默认/dev/input/event7): ").strip() or "/dev/input/event7"
    
    try:
        key_code = int(input("请输入按键码(默认139=菜单键): ") or "139")
    except ValueError:
        key_code = 139
    
    print()
    
    # 执行模拟
    simulate_sendevent_longpress(device_path, key_code, duration, dry_run)

def batch_demo():
    """批量演示不同按键"""
    print("\n🔄 批量演示不同按键")
    print("=" * 40)
    
    keys = [
        (139, "菜单键", "0x8b"),
        (116, "电源键", "0x74"),
        (158, "返回键", "0x9e"),
        (172, "主页键", "0xac"),
    ]
    
    for key_code, name, hex_code in keys:
        print(f"\n📱 演示 {name} (code={key_code}, hex={hex_code})")
        print("-" * 30)
        simulate_sendevent_longpress('/dev/input/event7', key_code, 1, dry_run=True)
        
        choice = input("继续下一个? (Y/n): ").lower().strip()
        if choice == 'n':
            break

def main():
    """主函数"""
    print("🚀 sendevent 长按按键模拟脚本")
    print("基于事件序列: /dev/input/event7: 0001 008b 00000001 ...")
    
    while True:
        print("\n📋 选择操作:")
        print("1. 事件序列分析")
        print("2. 模拟长按菜单键(2秒)")
        print("3. 交互式演示")
        print("4. 批量演示不同按键")
        print("5. 退出")
        
        choice = input("\n请选择 (1-5): ").strip()
        
        if choice == '1':
            show_event_sequence_analysis()
        elif choice == '2':
            simulate_sendevent_longpress()
        elif choice == '3':
            interactive_demo()
        elif choice == '4':
            batch_demo()
        elif choice == '5':
            print("👋 再见!")
            break
        else:
            print("❌ 无效选择，请重试")

if __name__ == "__main__":
    main()
