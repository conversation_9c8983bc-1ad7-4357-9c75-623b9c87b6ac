# ADB 长按按键时长控制指南

## 问题修复

### 原始问题
```bash
adb shell input keyevent --longpress keyevent 82
#                                   ^^^^^^^^ 语法错误：重复了keyevent
```

### 修复后
```bash
adb shell input keyevent --longpress 82  # ✅ 正确语法
```

## 按键时长控制方案

### 方案1：系统默认长按（推荐简单场景）
```bash
adb shell input keyevent --longpress 82
```
- **时长**：系统固定（通常500-1000ms）
- **优点**：简单直接
- **缺点**：无法自定义时长

### 方案2：使用 swipe 模拟长按（推荐自定义时长）
```bash
# 在屏幕中心点长按2秒
adb shell input swipe 500 500 500 500 2000

# 在指定坐标长按3秒
adb shell input swipe 100 200 100 200 3000
```
- **时长**：最后一个参数，单位毫秒
- **优点**：可自定义时长，兼容性好
- **缺点**：需要指定坐标

### 方案3：多次短按模拟长按
```bash
# 连续按键模拟长按效果
for i in {1..20}; do
    adb shell input keyevent 82
    sleep 0.1
done
```
- **时长**：通过循环次数和间隔控制
- **优点**：精确控制
- **缺点**：不是真正的长按

### 方案4：sendevent 精确控制（需要root）
```bash
# 按下按键
adb shell sendevent /dev/input/event0 1 82 1
adb shell sendevent /dev/input/event0 0 0 0

# 保持按下状态3秒
sleep 3

# 释放按键
adb shell sendevent /dev/input/event0 1 82 0
adb shell sendevent /dev/input/event0 0 0 0
```
- **时长**：通过 sleep 精确控制
- **优点**：最精确的控制
- **缺点**：需要root权限，设备路径可能不同

## 新增函数使用方法

### 1. 自定义时长长按菜单键
```python
# 长按菜单键2秒（默认）
adb_long_press_menu_custom()

# 长按菜单键5秒
adb_long_press_menu_custom(5000)
```

### 2. 自定义时长长按任意按键
```python
# 长按菜单键3秒
adb_long_press_keycode_custom(82, 3000)

# 长按电源键5秒
adb_long_press_keycode_custom(26, 5000)

# 长按返回键1秒
adb_long_press_keycode_custom(4, 1000)
```

## 常用按键码对照表

| 按键名称 | 按键码 | 说明 |
|---------|--------|------|
| 返回键 | 4 | KEYCODE_BACK |
| 主页键 | 3 | KEYCODE_HOME |
| 菜单键 | 82 | KEYCODE_MENU |
| 电源键 | 26 | KEYCODE_POWER |
| 音量+ | 24 | KEYCODE_VOLUME_UP |
| 音量- | 25 | KEYCODE_VOLUME_DOWN |
| 确认键 | 23 | KEYCODE_DPAD_CENTER |
| 上方向键 | 19 | KEYCODE_DPAD_UP |
| 下方向键 | 20 | KEYCODE_DPAD_DOWN |
| 左方向键 | 21 | KEYCODE_DPAD_LEFT |
| 右方向键 | 22 | KEYCODE_DPAD_RIGHT |

## 使用建议

1. **一般场景**：使用 `--longpress` 参数，简单有效
2. **需要自定义时长**：使用 swipe 方案，兼容性最好
3. **精确控制**：使用新增的自定义函数
4. **特殊需求**：使用 sendevent 方案（需要root）

## 注意事项

1. **坐标选择**：使用 swipe 方案时，建议选择屏幕中心或安全区域
2. **时长设置**：过长的按键时长可能导致意外行为
3. **设备兼容性**：不同设备对长按的响应可能不同
4. **权限要求**：sendevent 方案需要root权限
