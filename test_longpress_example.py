#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
长按按键测试示例
"""

import sys
import os
import time

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'script', 'testcases'))

try:
    from adb_command import adb_long_press_menu_custom, adb_long_press_keycode_custom, timeout_command
    
    def test_longpress_methods():
        """测试不同的长按方法"""
        print("=== 长按按键测试示例 ===\n")
        
        # 方法1: 系统默认长按（约1秒）
        print("1. 系统默认长按菜单键...")
        cmd = 'adb shell input keyevent --longpress 82'
        print(f"执行命令: {cmd}")
        # timeout_command(cmd)  # 取消注释以实际执行
        print("✓ 完成\n")
        
        # 方法2: 使用swipe模拟长按（可自定义时长）
        print("2. 使用swipe模拟长按3秒...")
        cmd = 'adb shell input swipe 500 500 500 500 3000'
        print(f"执行命令: {cmd}")
        # timeout_command(cmd)  # 取消注释以实际执行
        print("✓ 完成\n")
        
        # 方法3: 使用自定义函数长按菜单键
        print("3. 使用自定义函数长按菜单键2秒...")
        print("调用: adb_long_press_menu_custom(2000)")
        # adb_long_press_menu_custom(2000)  # 取消注释以实际执行
        print("✓ 完成\n")
        
        # 方法4: 使用自定义函数长按任意按键
        print("4. 使用自定义函数长按电源键5秒...")
        print("调用: adb_long_press_keycode_custom(26, 5000)")
        # adb_long_press_keycode_custom(26, 5000)  # 取消注释以实际执行
        print("✓ 完成\n")
        
        print("=== 测试完成 ===")
        print("注意：实际执行时请取消相关行的注释")
        
    def show_keycode_examples():
        """显示常用按键码示例"""
        print("\n=== 常用按键码示例 ===")
        examples = [
            (4, "返回键", "adb_long_press_keycode_custom(4, 2000)"),
            (3, "主页键", "adb_long_press_keycode_custom(3, 2000)"),
            (82, "菜单键", "adb_long_press_keycode_custom(82, 2000)"),
            (26, "电源键", "adb_long_press_keycode_custom(26, 3000)"),
            (24, "音量+", "adb_long_press_keycode_custom(24, 1000)"),
            (25, "音量-", "adb_long_press_keycode_custom(25, 1000)"),
        ]
        
        for keycode, name, example in examples:
            print(f"按键码 {keycode:2d} - {name:6s}: {example}")
        
    if __name__ == "__main__":
        test_longpress_methods()
        show_keycode_examples()
        
except ImportError as e:
    print(f"导入失败: {e}")
    print("请确保在正确的项目目录中运行此脚本")
except Exception as e:
    print(f"其他错误: {e}")
