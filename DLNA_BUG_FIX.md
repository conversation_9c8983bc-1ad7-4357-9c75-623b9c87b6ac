# DLNA 投屏测试异常修复报告

## 问题描述
在运行 DLNA 投屏测试时出现异常：
```
DLNA投屏测试异常: 'int' object has no attribute 'stdout'
```

## 问题分析

### 错误位置
文件：`script/smartshare/DLNA.py`
方法：`smartshare_phone2tv()`
行号：第80-82行

### 错误代码
```python
# 错误的写法
state = Popen("adb -s %s shell getprop ro.product.name" % self.tv_id, shell=True, stdout=PIPE,
              stderr=STDOUT).wait()  # ❌ .wait() 返回的是 int (退出码)
name = state.stdout.read().strip()   # ❌ int 没有 stdout 属性
```

### 问题根因
1. `Popen(...).wait()` 返回的是进程的退出码（整数），而不是 Popen 对象
2. 整数类型没有 `stdout` 属性
3. 当代码尝试访问 `state.stdout.read()` 时，Python 抛出 `AttributeError: 'int' object has no attribute 'stdout'`

## 解决方案

### 修复后的代码
```python
# 正确的写法
state = Popen("adb -s %s shell getprop ro.product.name" % self.tv_id, shell=True, stdout=PIPE,
              stderr=STDOUT)          # ✅ 先创建 Popen 对象
state.wait()                         # ✅ 等待进程完成
name = state.stdout.read().strip()   # ✅ 从 Popen 对象读取输出
```

### 修复原理
1. 先创建 `Popen` 对象并保存引用
2. 调用 `.wait()` 方法等待进程完成
3. 从 Popen 对象的 `stdout` 读取输出

## 验证结果
- ✅ 文件语法检查通过
- ✅ Python 编译检查通过
- ✅ 修复了 `'int' object has no attribute 'stdout'` 错误

## 其他检查
检查了整个 `DLNA.py` 文件中的所有 Popen 使用情况，确认：
- ✅ 其他所有的 `.stdout.read()` 使用都是正确的
- ✅ 没有发现类似的错误模式
- ✅ 文件中共有38处 Popen 使用，只有这一处有问题

## 影响范围
此修复解决了 DLNA 投屏功能中的关键错误，确保：
1. 腾讯视频投屏功能正常工作
2. 电视设备信息获取正常
3. DLNA 投屏测试流程不会因此异常中断

## 建议
为避免类似问题，建议在使用 `subprocess.Popen` 时：
1. 明确区分 Popen 对象和 `.wait()` 返回值
2. 使用 `.communicate()` 方法获取输出（更安全）
3. 添加异常处理机制
