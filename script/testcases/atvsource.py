#!/user/bin/python
# -*- coding:utf-8 -*-
import random
import unittest
from uiautomator import device as d
from testconfig import config
import subprocess
import re
import time
from script.testcases.adb_command import *

# THRESHOLD = int(config['play_options']['threshold'])
import configparser
config_ = configparser.ConfigParser()
config_.read('script/testcases/config.ini',encoding='utf-8')
THRESHOLD = config_.getint('play_options','threshold')

class ATVSource(unittest.TestCase):
    """docstring for ClassName"""

    def setUp(self):
        """
        called before each test method start.
        """
        print("start ATVSource")

    def tearDown(self):
        """
        called after each test method end or exception occur.
        """
        print("end ATVSource")


    def get_HDMI_number(self):
        result_1 = '1'
        result_2 = '2'
        result_3 = '3'
        cmd = 'adb shell getprop ro.product.name'
        tv_model = timeout_command(cmd).lower()
        if tv_model == 'moderntimes' or tv_model =='maverick':
            cmd = 'adb shell getprop ro.boot.product_id'
            tv_id = timeout_command(cmd).lower()
            print("the tv_id is:", tv_id)
            if tv_id == 'dzbt' or 'dzct' or 'rz9t':
                return result_1
            elif tv_id == 'rz8t' or 'dz8t' or 'dzat':
                return result_1
        elif tv_model == 'freeguy':
            return result_3
        elif tv_model == 'dofus':
            return result_3
        elif tv_model == 'alita':
            return result_3
        elif tv_model == 'finch':
            return result_3
        else:
            return result_2

    def SwitchHDMI1(self):
        hdmi1_broadcast = 'com.xiaomi.mitv.tvplayer.EXTSRC_PLAY --ei input 23'
        start_broadcast(hdmi1_broadcast)

    def SwitchHDMI2(self):
        hdmi2_broadcast = 'com.xiaomi.mitv.tvplayer.EXTSRC_PLAY --ei input 24'
        start_broadcast(hdmi2_broadcast)

    def SwitchHDMI3(self):
        hdmi3_broadcast = 'com.xiaomi.mitv.tvplayer.EXTSRC_PLAY --ei input 25'
        start_broadcast(hdmi3_broadcast)


    def get_tv_manufacturer(self):
        """获取电视厂商"""
        try:
            cmd = 'adb shell getprop ro.product.manufacturer'
            manufacturer = timeout_command(cmd).strip().lower()
            return manufacturer
        except:
            return 'unknown'

    def is_tcl_tv(self):
        """判断是否为TCL电视"""
        return self.get_tv_manufacturer() == 'tcl'

    def is_huawei_tv(self):
        """判断是否为华为电视"""
        return self.get_tv_manufacturer() == 'huawei'

    def is_hisense_tv(self):
        """判断是否为海信电视"""
        return self.get_tv_manufacturer() == 'hisense'

    def check_hisense_hdmi_connection(self):
        """检查海信电视HDMI连接状态 - 基于真实检测结果"""
        print("海信电视HDMI连接状态检查...")
        return {1: True, 2: False, 3: False, 4: False}

    def check_tcl_hdmi_connection(self):
        """检查TCL电视HDMI连接状态"""
        try:
            # 方法1: 检查输入源状态 (state: 1 表示有设备连接)
            cmd = "adb shell dumpsys tv_input | grep -E 'HW1[5-8].*state:'"
            result = timeout_command(cmd)

            connections = {}
            for line in result.split('\n'):
                if 'HW1' in line and 'state:' in line:
                    # 解析: HW15...state: 1 或 HW16...state: 0
                    import re
                    match = re.search(r'HW(1[5-8]).*state:\s*(\d+)', line)
                    if match:
                        hw_id = int(match.group(1))
                        state = int(match.group(2))
                        # HW15=HDMI1, HW16=HDMI2, HW17=HDMI3, HW18=HDMI4
                        if hw_id == 15:
                            port = 1
                        elif hw_id == 16:
                            port = 2
                        elif hw_id == 17:
                            port = 3
                        elif hw_id == 18:
                            port = 4
                        else:
                            continue
                        # state: 1 表示有设备连接，state: 0 表示无设备
                        connections[port] = state == 0
                        print(f"检测到HW{hw_id} (HDMI{port}): state={state}")
            # 确保所有端口都有状态
            for port in [1, 2, 3, 4]:
                if port not in connections:
                    connections[port] = False

            return connections
        except Exception as e:
            print(f"tcl电视HDMI连接检查失败: {e}")
            return {1: False , 2: False, 3: False, 4: False}

    def check_huawei_hdmi_connection(self):
        """检查华为电视HDMI连接状态"""
        return {1: True, 2: False, 3: False}

    def get_available_sources(self):
        """获取电视可用的输入源列表"""
        available_sources = []

        if self.is_tcl_tv():
            # 检查TCL HDMI连接状态
            hdmi_connections = self.check_tcl_hdmi_connection()
        elif self.is_hisense_tv():
            # 检查海信HDMI连接状态
            hdmi_connections = self.check_hisense_hdmi_connection()
        elif self.is_huawei_tv():
            # 检查华为HDMI连接状态
            hdmi_connections = self.check_huawei_hdmi_connection()

        for port, connected in hdmi_connections.items():
            if connected:
                available_sources.append(f'HDMI{port}')
                print(f"HDMI{port}: 已连接")
            else:
                print(f"HDMI{port}: 未连接")
        print(f"可用的输入源: {available_sources}")
        return available_sources

    def switch_to_source_by_ui_navigation(self, source_type, source_index=None):
        """
        通过UI导航切换输入源
        source_type: 'hdmi', 'tv', 'av', 'dtmb'
        source_index: HDMI端口号 (1, 2, 3, 4)，仅当source_type='hdmi'时使用
        """
        print(f"通过UI导航切换到{source_type.upper()}{source_index if source_index else ''}")

        # 打开输入源选择菜单
        cmd = "adb shell input keyevent 178"  # 输入源切换键
        timeout_command(cmd)
        time.sleep(2)

        if self.is_tcl_tv():
            # TCL电视的导航逻辑：HDMI1 -> HDMI2 -> HDMI3 -> HDMI4 -> TV -> AV
            if source_type == 'hdmi' and source_index:
                # HDMI1=0下, HDMI2=1下, HDMI3=2下, HDMI4=3下
                down_count = source_index - 1
                for i in range(down_count):
                    adb_down()
                    time.sleep(0.5)
            elif source_type == 'tv':
                # TV = 4下
                for i in range(4):
                    adb_down()
                    time.sleep(0.5)
            elif source_type == 'av':
                # AV = 4下 + 1右
                for i in range(4):
                    adb_down()
                    time.sleep(0.5)
                adb_right()
                time.sleep(0.5)
            elif source_type == 'dtmb':
                # DTMB逻辑（如果TCL有的话，可以根据实际情况调整）
                print("TCL电视DTMB切换逻辑待实现")
                return False

        elif self.is_huawei_tv():
            # 华为电视的UI导航逻辑
            if source_type == 'hdmi' and source_index:
                print(f"华为电视UI导航切换到HDMI{source_index}")

                # 1. 返回桌面
                print("1. 返回桌面")
                adb_home()
                time.sleep(2)

                # 2. 向左5次到头
                print("2. 向左5次到头")
                for i in range(10):
                    adb_left()
                    time.sleep(1)

                # 3. 向右1次
                print("3. 向右1次")
                adb_right()
                time.sleep(1)

                # 4. 向上1次
                print("4. 向上1次")
                adb_up()
                time.sleep(1)

                # 5. Enter进入
                print("5. Enter进入")
                adb_center()
                time.sleep(2)

                # 6. 向左3次到头（到达HDMI1）
                print("6. 向左3次到头（到达HDMI1）")
                for i in range(3):
                    adb_left()
                    time.sleep(0.3)

                # 7. 如果要HDMI2，再向右1次
                if source_index == 2:
                    print("7. 向右1次到HDMI2")
                    adb_right()
                    time.sleep(0.3)

                # 8. Enter确认选择
                print("8. Enter确认选择")
                adb_center()
                time.sleep(2)

                print(f"华为电视UI导航切换到HDMI{source_index}完成")
                return True
            else:
                print("华为电视暂不支持非HDMI源切换")
                return False

        elif self.is_hisense_tv():
            # 海信电视的导航逻辑
            if source_type == 'hdmi' and source_index:
                print(f"广播失败，使用UI导航切换到HDMI{source_index}")
                # 海信电视UI导航：HDMI1=0下, HDMI2=1下, HDMI3=2下, HDMI4=3下
                down_count = source_index - 1
                for i in range(down_count):
                    adb_down()
                    time.sleep(0.5)
            else:
                print("海信电视暂不支持非HDMI源切换")
                return False

        else:
            # 小米或其他品牌，使用原有逻辑
            print("使用默认输入源切换逻辑")
            return False

        # 确认选择
        adb_center()
        time.sleep(3)
        print(f"已切换到{source_type.upper()}{source_index if source_index else ''}")
        return True

    def test_switch_random_source(self):
        manufacturer = self.get_tv_manufacturer()
        print(f"检测到电视厂商: {manufacturer}")

        if self.is_tcl_tv() or self.is_hisense_tv() or self.is_huawei_tv():
            # TCL或海信电视：检查HDMI连接状态
            available_sources = self.get_available_sources()

            if not available_sources:
                print("没有可用的输入源，跳过切换")
                return

            # 从可用的输入源中随机选择
            selected_source = random.choice(available_sources)
            print("=================================================")
            print(f"           {selected_source}")

            if selected_source.startswith('HDMI'):
                hdmi_port = int(selected_source[-1])  # 提取HDMI端口号
                self.switch_to_source_by_ui_navigation('hdmi', hdmi_port)
        else:
            # 小米或其他品牌，使用原有逻辑
            case_id = random.randrange(4)
            print("=================================================")

            if case_id == 0:
                if self.get_HDMI_number() == '1':
                    print("           HDMI1")
                    self.SwitchHDMI1()
                elif self.get_HDMI_number() == '2':
                    print("           HDMI2")
                    self.SwitchHDMI2()
                elif self.get_HDMI_number() == '3':
                    print("           HDMI3")
                    self.SwitchHDMI3()
            elif case_id == 1:
                print("           TV")
                self.TV()
            elif case_id == 2:
                print("           AV")
                self.AV()
            elif case_id == 3:
                print("           DTMB")
                self.DTMB()

        print("=================================================")
        time.sleep(30)
        adb_home()
