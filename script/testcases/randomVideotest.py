#!/usr/bin/python
# -*- coding:utf-8 -*-

import threading
import time
import unittest
import random
import cv2

from script.testcases.adb_command import *
import uiautomator2 as u2

# 全局监控标志
_monitoring_active = False
_monitor_thread = None

# 全局缓存厂商信息，避免重复获取
_tv_manufacturer_cache = None

def get_tv_manufacturer():
    """获取电视厂商（带缓存）"""
    global _tv_manufacturer_cache
    if _tv_manufacturer_cache is None:
        try:
            _tv_manufacturer_cache = timeout_command('adb shell getprop ro.product.manufacturer').strip().lower()
            print(f"电视厂商: {_tv_manufacturer_cache}")
        except:
            _tv_manufacturer_cache = 'unknown'
    return _tv_manufacturer_cache

def is_xiaomi_tv():
    """判断是否为小米设备"""
    return 'xiaomi' in get_tv_manufacturer()

def is_tcl_tv():
    """判断是否为TCL电视"""
    return 'tcl' in get_tv_manufacturer()

def is_hisense_tv():
    """判断是否为海信电视"""
    return 'hisense' in get_tv_manufacturer()

def is_huawei_tv():
    """判断是否为华为电视"""
    return 'huawei' in get_tv_manufacturer()

def reset_manufacturer_cache():
    """重置厂商缓存（用于测试或设备切换）"""
    global _tv_manufacturer_cache
    _tv_manufacturer_cache = None



# 电视应用配置枚举
class TVApps:
    """电视应用配置管理"""

    # 优酷应用配置
    YOUKU = {
        'tcl': "com.cibn.tv/com.youku.tv.home.activity.HomeActivity",
        'xiaomi': "com.cibn.tv/com.youku.tv.home.activity.HomeActivity",
        'hisense': "com.cibn.tv/com.youku.tv.home.activity.HomeActivity",
        'huawei': "com.cibn.tv.huawei/com.youku.tv.home.activity.HomeActivity"
    }

    # 爱奇艺应用配置
    IQIYI = {
        'tcl': "com.tcl.qiyiguo/com.gala.video.app.epg.HomeActivity",
        'xiaomi': "com.gitvdemo.video/com.gala.video.app.epg.HomeActivity",
        'hisense': "com.hisensecpa.tv/com.gala.video.app.epg.HomeActivity",
        'huawei': "com.gala.video/com.gala.video.app.epg.HomeActivity"
    }

    # 云视听极光（腾讯视频TV版）应用配置
    YUNSHITING = {
        'tcl': "com.ktcp.csvideo/com.ktcp.video.activity.MainActivity",
        'xiaomi': "com.ktcp.video/.activity.MainActivity",
        'hisense': "com.ktcp.csvideo/com.ktcp.video.activity.MainActivity",
        'huawei': "com.ktcp.video/.activity.MainActivity"
    }

    # B站应用配置
    BILIBILI = {
        'tcl': "com.xiaodianshi.tv.yst/.ui.main.MainActivity",
        'xiaomi': "com.xiaodianshi.tv.yst/.ui.main.MainActivity",
        'hisense': "com.xiaodianshi.tv.yst/.ui.main.MainActivity",
        'huawei': "com.xiaodianshi.tv.yst/.ui.main.MainActivity"
    }

    # 芒果TV应用配置
    MANGO = {
        'tcl': "com.hunantv.market/com.mgtv.tv.launcher.ChannelHomeActivity",
        'xiaomi': "com.hunantv.license/com.mgtv.tv.launcher.ChannelHomeActivity",
        'hisense': "com.hunantv.license/com.mgtv.tv.launcher.ChannelHomeActivity",
        'huawei': "com.hunantv.license/com.mgtv.tv.launcher.ChannelHomeActivity"
    }

    # 快手应用配置
    KUAISHOU = {
        'tcl': "com.kwai.tv.yst/com.yxcorp.gifshow.HomeActivity",
        'xiaomi': "com.kwai.tv.yst/com.yxcorp.gifshow.HomeActivity",
        'hisense': "com.kwai.tv.yst/com.yxcorp.gifshow.HomeActivity",
        'huawei': "com.kwai.tv.yst/com.yxcorp.gifshow.HomeActivity"
    }

    # 媒体浏览器应用配置
    MEDIA_EXPLORER = {
        'tcl': "com.tcl.ui_mediaCenter/.main.MainActivity",
        'xiaomi': "com.xiaomi.mitv.mediaexplorer/com.xiaomi.mitv.mediaexplorer.NewScraperMainEntryActivity",
        'hisense': "com.jamdeo.tv.mediacenter/.MediaCenterActivity",
        'huawei': None  # 华为没有对应应用
    }

    @staticmethod
    def get_app_name(app_type, manufacturer):
        """
        获取指定厂商和应用类型的应用名称

        Args:
            app_type: 应用类型 (如 TVApps.YOUKU)
            manufacturer: 厂商名称 (tcl/xiaomi/hisense/huawei)

        Returns:
            str: 应用包名/Activity，如果未找到返回 None
        """

        app_config = app_type
        manufacturer_key = manufacturer.lower()

        return app_config.get(manufacturer_key, None)

    @staticmethod
    def get_app_by_manufacturer(app_type):
        """
        根据当前设备厂商获取应用名称

        Args:
            app_type: 应用类型 (如 TVApps.YOUKU)

        Returns:
            str: 应用包名/Activity，如果未找到返回 None
        """
        if is_tcl_tv():
            manufacturer = 'tcl'
        elif is_xiaomi_tv():
            manufacturer = 'xiaomi'
        elif is_hisense_tv():
            manufacturer = 'hisense'
        elif is_huawei_tv():
            manufacturer = 'huawei'
        else:
            return None

        return TVApps.get_app_name(app_type, manufacturer)

def cleanup_uiautomator():
    """彻底清理UIAutomator2服务"""
    try:
        print("开始清理UIAutomator2服务...")

        # 1. 停止所有相关进程
        timeout_command('adb shell am force-stop com.github.uiautomator', timeout=10)
        timeout_command('adb shell am force-stop com.github.uiautomator.test', timeout=10)
        timeout_command('adb shell killall uiautomator', timeout=10)
        timeout_command('adb shell killall com.github.uiautomator', timeout=10)

        # 2. 清理UIAutomator相关进程（更彻底）
        timeout_command('adb shell "ps | grep uiautomator | awk \'{print $2}\' | xargs kill -9"', timeout=10)
        timeout_command('adb shell "ps | grep atx-agent | awk \'{print $2}\' | xargs kill -9"', timeout=10)

        # 3. 清理accessibility服务注册冲突
        timeout_command('adb shell settings put secure enabled_accessibility_services ""', timeout=10)
        # 强制清理accessibility服务缓存
        timeout_command('adb shell "pm clear com.android.providers.settings"', timeout=10)
        time.sleep(3)

        # 4. 清理端口占用（杀死占用端口的进程）
        cleanup_ports()

        # 5. 强制清理UIAutomation服务状态
        timeout_command('adb shell "dumpsys accessibility | grep -i uiautomation"', timeout=5)
        timeout_command('adb shell "service call accessibility 1"', timeout=5)  # 重置accessibility服务

        # 6. 重启atx-agent
        timeout_command('adb shell pkill -f atx-agent', timeout=10)
        time.sleep(3)
        timeout_command('adb shell /data/local/tmp/atx-agent server -d --stop', timeout=10)
        time.sleep(3)

        # 7. 清理可能的僵尸进程
        timeout_command('adb shell "ps -A | grep -E \'(uiautomator|atx-agent)\' | awk \'{print $2}\' | xargs kill -9"', timeout=10)
        time.sleep(2)

        # 8. 重新启动atx-agent
        timeout_command('adb shell /data/local/tmp/atx-agent server -d', timeout=10)
        time.sleep(5)

        print("UIAutomator2服务清理完成")
        return True
    except Exception as e:
        print(f"清理UIAutomator2服务失败: {e}")
        return False

def force_kill_uiautomator_processes():
    """强制杀死所有UIAutomator相关进程"""
    try:
        print("强制清理UIAutomator进程...")

        # 获取所有相关进程并强制杀死
        processes_to_kill = [
            'uiautomator', 'atx-agent', 'com.github.uiautomator',
            'UiAutomator2Server', 'InstrumentationTestRunner'
        ]

        for process in processes_to_kill:
            timeout_command(f'adb shell "ps -A | grep {process} | awk \'{{print $2}}\' | xargs kill -9"', timeout=5)

        # 清理网络连接
        timeout_command('adb shell "netstat -tulpn | grep -E \'(7912|9008|38439|38440|38441|38442|38443)\' | awk \'{print $7}\' | cut -d/ -f1 | xargs kill -9"', timeout=5)

        time.sleep(2)
        print("UIAutomator进程强制清理完成")
        return True
    except Exception as e:
        print(f"强制清理UIAutomator进程失败: {e}")
        return False

def cleanup_ports():
    """清理端口占用"""
    try:
        print("清理端口占用...")
        # 常见的UIAutomator2端口范围
        common_ports = [6790, 7912, 9008]

        for port in common_ports:
            # 查找占用端口的进程并杀死
            timeout_command(f'adb shell "netstat -tulpn | grep :{port} | awk \'{{print $7}}\' | cut -d/ -f1 | xargs kill -9"', timeout=5)
            # 额外检查：使用ss命令（某些Android版本）
            timeout_command(f'adb shell "ss -tulpn | grep :{port} | awk \'{{print $6}}\' | cut -d, -f2 | cut -d= -f2 | xargs kill -9"', timeout=5)

        # 额外清理：查找所有UIAutomator2相关端口
        timeout_command('adb shell "netstat -tulpn | grep uiautomator | awk \'{print $7}\' | cut -d/ -f1 | xargs kill -9"', timeout=10)
        timeout_command('adb shell "ss -tulpn | grep uiautomator | awk \'{print $6}\' | cut -d, -f2 | cut -d= -f2 | xargs kill -9"', timeout=10)

        # 清理可能的监听端口
        timeout_command('adb shell "lsof -i | grep -E \'(7912|9008|38439|38440|38441|38442|38443)\' | awk \'{print $2}\' | xargs kill -9"', timeout=5)

        time.sleep(3)
        print("端口清理完成")
    except Exception as e:
        print(f"端口清理失败: {e}")

# 全局设备连接变量
d = None

def init_device():
    """初始化设备连接"""
    global d
    try:
        # 先清理可能存在的UIAutomator2服务冲突
        cleanup_uiautomator()

        # 连接设备
        d = u2.connect()
        d.screen_on()

        # 验证连接是否正常
        if verify_device_connection():
            print("设备连接成功")
            return True
        else:
            print("设备连接验证失败")

    except Exception as e:
        print(f"init_device 设备连接失败 {e}")
    return False

def verify_device_connection():
    """验证设备连接是否正常"""
    try:
        # 测试基本的UIAutomator2功能
        d.info
        return True
    except Exception as e:
        print(f"设备连接验证失败: {e}")
        return False

# 尝试初始化设备连接
print("正在初始化设备连接...")
if not init_device():
    print("警告: 设备初始化失败，测试可能不稳定")

def setup_watchers():
    """设置UIAutomator2 watchers"""
    try:
        # 先停止并清理现有的watchers
        try:
            d.watcher.stop()
            d.watcher.remove()
            time.sleep(2)  # 增加等待时间
        except Exception as e:
            print(f"停止watchers时出错: {e}")
            pass

        # 验证设备连接状态
        if not verify_device_connection():
            print("设备连接异常，无法设置watchers")
            return False

        # 设置watchers
        d.watcher.when("升级").click()
        d.watcher.when("完成").click()
        d.watcher.when("安装").click()
        d.watcher.when("退出").click()
        d.watcher.when("确定退出电视猫视频？").click()
        d.watcher.when("是否退出迅雷看看？").click()
        d.watcher.when("您要安装此应用的新版本吗？").click()
        d.watcher.when("主人，您真的要离开吗？记得常来看我呀").click()
        d.watcher.when("确认").click()
        d.watcher.when("下次更新").click()
        d.watcher.when("已阅读").press("enter")
        d.watcher.when("根据国家现行政策规定").press("enter")
        d.watcher.when("无响应").click()
        d.watcher.when("停止运行").click()
        d.watcher.when("确认退出").click()
        d.watcher.when("按【返回】可以关闭弹窗哦").press("back")
        d.watcher.when("按返回键退出播放").press("back")
        d.watcher.when("访问您设备上的照片、媒体内容和文件吗？").press("enter")
        d.watcher.when("按【返回键】").press("back")
        d.watcher.when("用户协议及隐私政策 ").press("enter")
        d.watcher.when("我知道了").press("enter")
        d.watcher.when("同意").press("enter")
        d.watcher.when("允许CIBN聚体育拨打电话和管理通话吗？").press("enter")
        d.watcher.when("再看看").press("enter")
        d.watcher.when("再看一会儿").press("enter")

        # 启动 watchers
        d.watcher.start()
        time.sleep(1)  # 给watchers启动时间

        print("watchers 设置完成")
        return True

    except Exception as e:
        print(f"watchers 设置失败: {e}")

    return False

def reconnect_device():
    """重连设备并重新初始化"""
    global d
    # 1. 强制清理所有UIAutomator进程
    force_kill_uiautomator_processes()
    time.sleep(1)
    # 2. 彻底清理UIAutomator2服务
    cleanup_uiautomator()

    # 3. 等待服务完全停止
    time.sleep(3)

    # 4. 重新连接设备
    try:
        d = u2.connect()
    except Exception as connect_error:
        print(f"u2.connect()失败: {connect_error}")
        if "already registered" in str(connect_error).lower():
            print("检测到服务已注册错误，强制清理后重试...")
            force_kill_uiautomator_processes()
            time.sleep(5)
            d = u2.connect()
        else:
            raise connect_error

    # 5. 验证连接
    if not verify_device_connection():
        print(f"设备连接验证失败，继续重试...")
        return False

    d.screen_on()

    # 6. 重新设置watchers
    if setup_watchers():
        print("设备重连成功")
        return True
    else:
        print("watchers设置失败，继续重试...")
    return False

def ui_safe(func):
    """UIAutomator2安全装饰器"""
    def wrapper(self, *args, **kwargs):
        max_retries = 5

        for attempt in range(max_retries):
            try:
                return func(self, *args, **kwargs)
            except Exception as e:
                error_msg = str(e).lower()
                print(f'{func.__name__} UIAutomator2异常 (尝试 {attempt + 1}): {e}')

                # 检查是否是UI元素未找到的错误（这类错误不需要重连）
                ui_element_errors = [
                    'uiobjectnotfoundexception', 'nosuchelementexception',
                    'element not found', 'selector not found',
                    'no matching element', 'element does not exist'
                ]

                is_ui_element_error = any(err in error_msg for err in ui_element_errors)

                if is_ui_element_error:
                    print(f"检测到UI元素未找到错误，continue")
                    continue

                # 检查是否是连接相关的错误
                connection_errors = [
                    'connection aborted', 'remote end closed connection',
                    'uiautomationservice', 'already registered',
                    'address already in use', 'connection refused',
                    'uiautomation not connected', 'http request failed',
                    'invalid literal for int() with base'
                ]

                is_connection_error = any(err in error_msg for err in connection_errors)

                if is_connection_error and attempt < max_retries - 1:
                    print(f"检测到连接错误，尝试重连设备...")
                    # 尝试重连设备
                    if reconnect_device():
                        print(f"设备重连成功，重试 {func.__name__}")
                    else:
                        continue
                else:
                    # 非连接错误或已达到最大重试次数
                    if attempt < max_retries - 1:
                        print(f"非连接错误或达到重试上限，直接重试...")
                        time.sleep(2)
                        continue
                    break

        print(f'{func.__name__} 所有重试都失败了')
        return False
    return wrapper

class RandomPlayerTest(unittest.TestCase):
    def setUp(self):
        """
        called before each test method start.
        """
        print("=" * 50)
        print("开始测试用例初始化...")
        print("=" * 50)
        self.dlna_handle = self.init_dlna_for_test()
        try:
            # 验证设备连接状态
            if not verify_device_connection():
                if not reconnect_device():
                    print("设备连接失败，测试可能不稳定")
                    return False
            # 设置watchers
            if not setup_watchers():
                return False

            # 唤醒屏幕
            d.screen_on()
            time.sleep(5)

            print("测试用例初始化完成")
        except Exception as e:
            if not reconnect_device() and not setup_watchers():
                time.sleep(5)
            else:
                print("初始化失败，设备连接异常")

        back2home_page()

    def tearDown(self):
        """
        called after each test method end or exception occur.
        """
        print("=" * 50)
        print("开始测试用例清理...")
        print("=" * 50)

        # 1. 停止watchers
        try:
            if d and hasattr(d, 'watcher'):
                d.watcher.stop()  # 停止所有watchers
                d.watcher.remove()  # 移除所有watchers
                time.sleep(2)  # 给更多时间让watchers完全停止
                print("watchers清理完成")
        except Exception as e:
            print(f"停止watchers时出错: {e}")

        # 2. 返回主页
        back2home_page()
        print("返回主页成功")

        # 3. 检查设备状态
        try:
            if not verify_device_connection():
                print("警告: 设备连接状态异常")
        except Exception as e:
            print(f"设备状态检查失败: {e}")

        print("测试用例清理完成")
        print("=" * 50)

    def init_dlna_for_test(self):
        """为测试初始化DLNA处理器"""
        try:
            from script.smartshare.DLNA import DLNAshare
            dlna = DLNAshare()
            dlna.setUp()  # 调用DLNA的setUp方法初始化手机连接
            print(f"DLNA初始化成功 - 手机ID: {dlna.phone_id}, 电视ID: {dlna.tv_id}")
            return dlna
        except Exception as e:
            print(f"DLNA初始化失败: {e}")
            return None

    @ui_safe
    def test_dlna_tencent_casting(self):
        """调用DLNA.py的腾讯视频投屏方法"""
        back2home_page()
        app_name = TVApps.get_app_by_manufacturer(TVApps.YUNSHITING)
        if not app_name:
            print("当前厂商不支持云视听极光应用")
            return False
        print(f"投屏启动腾讯视频: {app_name}")
        start_app(app_name)
        package = get_front_package()
        adb_right()  # 跳过广告
        time.sleep(2)
        if "com.ktcp.video/.activity.PrivacyAgreementActivity" in package:
            adb_center()
            time.sleep(2)
        # 跳过弹窗
        if "com.ktcp.csvideo/com.tencent.qqlivetv.widget.popup.TvCommonPopupActivity" in package:
            adb_right()
            time.sleep(2)
            adb_center()
            time.sleep(2)
        try:
            # 2. 调用DLNA的腾讯视频投屏方法
            print("开始手机腾讯视频投屏...")
            self.dlna_handle.smartshare_phone2tv()

            # 3. 检查电视端投屏接收
            print("检查电视端投屏状态...")
            if not self.dlna_handle.check_tv():
                print("电视端投屏接收失败")
                return False
            return True

        except Exception as e:
            print(f"DLNA投屏测试异常: {e}")
            return False


    def video_interaction_operations(self):
        """
        通用的视频交互操作
        包括截图、seek操作、智能助手、小爱同学等
        """
        print("开始视频交互操作...")

        # 截图
        self.screenshot()
        time.sleep(5)

        # 反复seek操作
        [adb_left() for i in range(5)]
        [adb_right() for i in range(5)]
        print('反复seek')
        time.sleep(5)

        # 调出智能助手
        adb_long_pressmenu()
        print('调出智能助手')
        time.sleep(3)
        adb_back()
        time.sleep(5)

        # 唤醒小爱同学
        call_xiaoai()
        time.sleep(3)
        adb_back()
        print('唤醒小爱')

        # 暂停操作
        [adb_center() for i in range(2)]
        print('暂停')
        time.sleep(30)

        # 退出
        [adb_back() for i in range(2)]
        print('视频交互操作完成')
        return True

    def is_screen_on(self):
        """
        检查电视屏幕是否亮屏
        :return: 屏幕是否亮屏
        """
        try:
            if is_xiaomi_tv():
                check_screen = timeout_command('adb shell getprop sys.screen.turn_on')
                print("xiaomi check_screen: {check_screen}")
                if 'true' in check_screen:
                    return True
                else:
                    return False
            if is_tcl_tv() or is_hisense_tv() or is_huawei_tv():
                check_screen = timeout_command(f'adb shell dumpsys display | grep mState=')
                print("tcl check_screen: {check_screen}")
                if 'ON' in check_screen:
                    return True
                else:
                    return False
        except Exception as e:
            print(f"检查屏幕状态异常: {e}")
        return True

    def enter_exit_str(self):
        max_retries = 5
        if is_xiaomi_tv():
            # 获取当前屏幕状态
            check_screen = self.is_screen_on()
            if check_screen:
                print("屏幕已亮，开始熄屏")
                # 尝试熄屏，重试5次
                for i in range(max_retries):
                    press_power()
                    time.sleep(10)
                    check_screen = self.is_screen_on()
                    if not check_screen:
                        print(f"屏幕熄灭成功 (第{i + 1}次尝试)")
                        break
                    else:
                        print(f"屏幕熄灭失败，重试 (第{i + 1}次尝试)")
                else:
                    print("屏幕熄灭失败，已重试5次")
                    return False
                # 熄屏成功后，尝试亮屏，重试5次
                print("屏幕已熄灭，开始亮屏")
                for i in range(max_retries):
                    press_power()
                    time.sleep(10)
                    check_screen = self.is_screen_on()
                    if check_screen:
                        print(f"屏幕亮屏成功 (第{i + 1}次尝试)")
                        return True
                    else:
                        print(f"屏幕亮屏失败，重试 (第{i + 1}次尝试)")

                print("屏幕亮屏失败，已重试5次")
                return False
            else:
                print("屏幕已熄灭，开始亮屏")
                # 尝试亮屏，重试5次
                for i in range(max_retries):
                    press_power()
                    time.sleep(5)
                    check_screen = self.is_screen_on()
                    if check_screen:
                        print(f"屏幕亮屏成功 (第{i + 1}次尝试)")
                        return True
                    else:
                        print(f"屏幕亮屏失败，重试 (第{i + 1}次尝试)")

                print("屏幕亮屏失败，已重试5次")
                return False

        if is_tcl_tv():
            # 获取当前屏幕状态
            check_screen = self.is_screen_on()
            if check_screen:
                print("屏幕已亮，开始熄屏")
                # 尝试熄屏，重试5次
                for i in range(max_retries):
                    self.dlna_handle.remote_control_operation("TCL")
                    time.sleep(5)
                    check_screen = self.is_screen_on()
                    if not check_screen:
                        print(f"屏幕熄灭成功 (第{i + 1}次尝试)")
                        break
                    else:
                        print(f"屏幕熄灭失败，重试 (第{i + 1}次尝试)")
                else:
                    print("屏幕熄灭失败，已重试5次")
                    return False
                # 熄屏成功后，尝试亮屏，重试5次
                print("屏幕已熄灭，开始亮屏")
                for i in range(max_retries):
                    self.dlna_handle.remote_control_operation("TCL")
                    time.sleep(5)
                    check_screen = self.is_screen_on()
                    if check_screen:
                        print(f"屏幕亮屏成功 (第{i + 1}次尝试)")
                        return True
                    else:
                        print(f"屏幕亮屏失败，重试 (第{i + 1}次尝试)")

                print("屏幕亮屏失败，已重试5次")
            else:
                print("屏幕已熄灭，开始亮屏")
                # 尝试亮屏，重试5次
                for i in range(max_retries):
                    self.dlna_handle.remote_control_operation("TCL")
                    time.sleep(5)
                    check_screen = self.is_screen_on()
                    if check_screen:
                        print(f"屏幕亮屏成功 (第{i + 1}次尝试)")
                        return True
                    else:
                        print(f"屏幕亮屏失败，重试 (第{i + 1}次尝试)")

                print("屏幕亮屏失败，已重试5次")
            return False
        if is_huawei_tv():
            check_screen = self.is_screen_on()
            if check_screen:
                print("华为屏幕已亮，开始熄屏")
                # 尝试熄屏，重试5次
                for i in range(max_retries):
                    adb_power()
                    time.sleep(1)
                    adb_left()
                    time.sleep(1)
                    adb_center()
                    time.sleep(1)
                    check_screen = self.is_screen_on()
                    if not check_screen:
                        print(f"屏幕熄灭成功 (第{i + 1}次尝试)")
                        break
                    else:
                        print(f"屏幕熄灭失败，重试 (第{i + 1}次尝试)")
                else:
                    print("屏幕熄灭失败，已重试5次")
                    return False
                # 熄屏成功后，尝试亮屏，重试5次
                print("屏幕已熄灭，开始亮屏")
                for i in range(max_retries):
                    adb_power()
                    time.sleep(3)
                    check_screen = self.is_screen_on()
                    if check_screen:
                        print(f"屏幕亮屏成功 (第{i + 1}次尝试)")
                        return True
                    else:
                        print(f"屏幕亮屏失败，重试 (第{i + 1}次尝试)")

                print("屏幕亮屏失败，已重试5次")
            else:
                print("屏幕已熄灭，开始亮屏")
                # 尝试亮屏，重试5次
                for i in range(max_retries):
                    adb_power()
                    time.sleep(3)
                    check_screen = self.is_screen_on()
                    if check_screen:
                        print(f"屏幕亮屏成功 (第{i + 1}次尝试)")
                        return True
                    else:
                        print(f"屏幕亮屏失败，重试 (第{i + 1}次尝试)")

                print("屏幕亮屏失败，已重试5次")
            return False
        if is_hisense_tv():
            # 获取当前屏幕状态
            check_screen = self.is_screen_on()
            if check_screen:
                print("屏幕已亮，开始熄屏")
                # 尝试熄屏，重试5次
                self.dlna_handle.remote_control_operation("海信")
                print("海信屏幕熄灭")
                time.sleep(8)
                print("屏幕已熄灭，开始亮屏")
                self.dlna_handle.remote_control_operation("海信")
                print("海信息屏断adb 需等待adb重连")
                time.sleep(60)
                check_screen = self.is_screen_on()
                if check_screen:
                    print(f"屏幕亮屏成功")
                    return True
                return False
            else:
                print("屏幕已熄灭，开始亮屏")
                # 尝试亮屏，重试5次
                self.dlna_handle.remote_control_operation("海信")
                time.sleep(5)
                check_screen = self.is_screen_on()
                if check_screen:
                    print(f"屏幕亮屏成功")
                    return True
                else:
                    print(f"屏幕亮屏失败")
                return False
        return False


    def exit_app_playback(self, app_type):
        """
        通用的应用播放退出逻辑
        :param app_type: 应用类型 ('iqiyi', 'youku', 'tencent', 'mango', 'bilibili', 'kuaishou', 'localvideo', 'online', 'children', 'dlna_casting')
        """
        print(f'退出{app_type}播放')

        if app_type == 'dlna_casting':
            # DLNA投屏退出逻辑
            print("执行DLNA投屏退出清理...")
            adb_home()  # 电视返回主页
            # 手机端断开会在测试方法的finally块中处理

        elif app_type in ['iqiyi', 'youku', 'mango']:
            # 爱奇艺、优酷、芒果TV: 返回4次 + 确认
            [adb_back() for i in range(4)]
            adb_center()

        elif app_type == 'tencent':
            # 腾讯视频: 返回4次 + 确认
            [adb_back() for i in range(4)]
            adb_center()

        elif app_type in ['bilibili', 'kuaishou']:
            # B站、快手: 返回3次 + 确认
            [adb_back() for i in range(3)]
            adb_center()

        elif app_type == 'localvideo':
            # 本地视频: 返回6次
            [adb_back() for i in range(6)]

        elif app_type in ['online', 'children']:
            [adb_back() for i in range(5)]
        else:
            # 默认退出方式: 返回3次
            print(f"使用默认退出方式: {app_type}")
            [adb_back() for i in range(3)]

        print(f'{app_type}播放退出完成')

    @ui_safe
    def testOnline_video(self):
        """
        随机播放桌面主页中一个视频
        应该播放第一集不需要会员吧
        :return:
        """
        start_time = time.time()
        adb_home()
        time.sleep(5)
        if is_hisense_tv():
            adb_back()
            time.sleep(2)
            adb_center()
            time.sleep(2)
            adb_center()
            print("进入海信桌面视频")
            return True
        if is_huawei_tv():
            adb_center()
            time.sleep(2)
            adb_down()
            time.sleep(2)
            adb_center()
            print("进入华为桌面视频")
            return True
        if is_tcl_tv():
            adb_down()
            time.sleep(2)
            adb_center()
            time.sleep(2)
            adb_down()
            time.sleep(2)
            adb_right()
            time.sleep(2)
            adb_center()
            time.sleep(2)
            adb_center()
            print("进入TCL桌面视频")
            return True
        if is_xiaomi_tv():
            """
            随机播放桌面主页中一个视频
            应该播放第一集不需要会员吧
            :return:
            """
            start_time = time.time()
            adb_home()
            time.sleep(5)
            adb_back()
            time.sleep(2)
            while time.time() - start_time < 300:
                if d(text='首页').exists():
                    d(text='首页').click()
                    adb_right()
                    adb_center()
                    try:
                        if d(text='全屏').exists():
                            d(text='全屏').click()
                            print('播放视频')
                            time.sleep(2)
                            return True
                        else:
                            adb_back()
                    except Exception as e:
                        print(f"查找全屏按钮时出错: {e}")
                        adb_back()
                else:
                    adb_back()
            print('播放失败，请重试')
            return False
        return False

    @ui_safe
    def test_children(self):
        """
        随机播放儿童模块主页中一个视频
        应该播放第一集不需要会员吧
        :return:
        """
        start_time = time.time()
        time.sleep(5)
        adb_home()
        if is_xiaomi_tv():
            adb_back()
            time.sleep(2)
            adb_back()
            time.sleep(2)
            while time.time() - start_time < 300:
                try:
                    if d(text='儿童').exists():
                        d(text='儿童').click()
                        try:
                            if d(text='同意并继续').exists():
                                d(text='同意并继续').click()
                                time.sleep(1)
                        except Exception as e:
                            print(f"查找同意按钮时出错: {e}")
                        adb_down()
                        time.sleep(1)
                        adb_center()
                        try:
                            if d(text='全屏').exists():
                                d(text='全屏').click()
                                print('播放视频')
                                time.sleep(2)
                                return True
                            else:
                                adb_back()
                        except Exception as e:
                            print(f"查找全屏按钮时出错: {e}")
                            adb_back()
                    else:
                        adb_back()
                except Exception as e:
                    print(f"查找儿童按钮时出错: {e}")
                    adb_back()
                    time.sleep(1)
            else:
                print('播放失败，请重试')
                return False
        elif is_hisense_tv():
            adb_left()
            time.sleep(2)
            adb_down()
            time.sleep(2)
            for i in range(3):
                adb_left()
                time.sleep(1)
            adb_right()
            time.sleep(2)
            adb_center()
            time.sleep(2)
            adb_left()
            time.sleep(2)
            adb_center()
            print("海信进入儿童模式")
            return True
        elif is_tcl_tv():
            print("tcl儿童模式需要会员，现在没有开通")
            return False
        elif is_huawei_tv():
            adb_center()
            time.sleep(2)
            adb_left()
            time.sleep(2)
            adb_down()
            time.sleep(2)
            for i in range(3):
                adb_left()
                time.sleep(1)
            adb_right()
            time.sleep(2)
            adb_center()
            time.sleep(2)
            print("华为进入儿童模式")
            return True
        else:
            print("其他机型，跳过儿童模式")
            return False

    def testYunshiting(self,count=0):
        back2home_page()
        app_name = TVApps.get_app_by_manufacturer(TVApps.YUNSHITING)
        if not app_name:
            print("当前厂商不支持云视听极光应用")
            return False

        print(f"启动腾讯视频: {app_name}")
        start_app(app_name)
        package = get_front_package()
        adb_right()  # 跳过广告
        time.sleep(2)
        if "com.ktcp.video/.activity.PrivacyAgreementActivity" in package:
            adb_center()
            time.sleep(2)
        # 跳过弹窗
        if "com.ktcp.csvideo/com.tencent.qqlivetv.widget.popup.TvCommonPopupActivity" in package:
            adb_right()
            time.sleep(2)
            adb_center()
            time.sleep(2)
        [adb_left() for i in range(2)]
        time.sleep(2)
        adb_down()
        time.sleep(2)
        adb_right()
        time.sleep(2)
        adb_center()
        tvqs_screeencap(screenshot_2=1, interation=1, delay_start=0, interval_time=30,
            rename_keyword="playcheck", screen_count=1)
        if click_by_image('yunshiting'):
            print('播放视频')
            return True
        return False

    @ui_safe
    def testBilibili(self):
        back2home_page()
        start_time = time.time()
        # 使用枚举获取应用配置
        app_name = TVApps.get_app_by_manufacturer(TVApps.BILIBILI)
        if not app_name:
            print("当前厂商不支持B站应用")
            return False
        print(f"启动B站: {app_name}")
        start_app(app_name)
        package = get_front_package()
        # assert app_name.split("/")[0] in package  # 判断是否启动了该App
        # 处理弹窗
        if "com.xiaodianshi.tv.yst/com.xiaodianshi.tv.yst.ui.messagedialog.MessageDialogActivity" in package:
            adb_back()
        if "com.xiaodianshi.tv.yst/.ui.messagedialog.MessageDialogActivity" in package:
            adb_back()
        # 同意用户协议
        if "com.xiaodianshi.tv.yst/.ui.introduction.IntroductionActivity" in package:
            adb_center()
        while time.time() - start_time < 300:
            if d(text='推荐').exists(timeout=10):
                d(text="推荐").click()
                adb_down()
                time.sleep(1)
                adb_center()
                print('播放bilibili视频')
                return True
            else:
                adb_back()
        return False

    @ui_safe
    def testAiQiYi(self):
        back2home_page()
        start_time = time.time()
        # 使用枚举获取应用配置
        app_name = TVApps.get_app_by_manufacturer(TVApps.IQIYI)
        if not app_name:
            print("当前厂商不支持爱奇艺应用")
            return False

        print(f"启动爱奇艺: {app_name}")
        start_app(app_name)

        if d(textContains="同意并继续").exists():  # 同意用户协议
            adb_center()
            time.sleep(2)
        # 存储权限
        package = get_front_package()
        if "com.android.permissioncontroller/.permission.ui.GrantPermissionsActivity" not in package:
            adb_center()
            time.sleep(2)
        print("退出广告页推送")
        adb_down()
        time.sleep(1)
        adb_back()
        time.sleep(1)
        adb_down()
        time.sleep(1)
        adb_up()
        while time.time() - start_time < 300:
            if d(text='标准').exists(timeout=10):
                adb_right()
                time.sleep(2)
                adb_center()
                time.sleep(2)
                print('播放视频')
                return True
            else:
                adb_back()
        return False

    @ui_safe
    def testYouku(self):
        back2home_page()
        # 使用枚举获取应用配置
        app_name = TVApps.get_app_by_manufacturer(TVApps.YOUKU)
        if not app_name:
            print("当前厂商不支持优酷应用")
            return False
        print(f"启动优酷: {app_name}")
        start_app(app_name)
        start_time = time.time()
        package = get_front_package()

        # time.sleep(2)
        # d.wait.update(timeout=2000, package_name='com.cibn.tv')
        if d(textContains="同意").exists():
            d(textContains="同意").click()
                # adb_center()
            time.sleep(2)

        try:
            # 处理自启动弹窗
            if d(textContains="是否将优酷XL设置为开机自启动？").wait(timeout=6.0):
                d.press("right")
                time.sleep(2)
                d.press("enter")

            # 处理更新弹窗
            if d(textContains="立即更新").wait(timeout=6.0):
                d.press("back")

            # 处理权限弹窗
            if d(textContains="允许").exists():
                d(textContains="允许").click()

        except Exception as e:
            print(f"处理弹窗时出错: {e}")
        time.sleep(20)
        while time.time() - start_time < 300:
            if d(text='首页').exists():
                d(text='首页').click()
                adb_down()
                time.sleep(2)
                adb_right()
                time.sleep(2)
                adb_center()
                time.sleep(2)
                # 尝试播放按钮
                if d(text='全屏播放').exists():
                    d(text='全屏播放').click()
                    print("点击了'全屏播放'")
                    return True
                elif d(textContains='播放第').exists():
                    d(textContains='播放第').click()
                    print("点击了包含'播放第'的元素")
                    return True
                print("优酷未找到播放按钮")
                adb_back()
            else:
                adb_back()
        return False

    @ui_safe
    def test_Mangotv(self):
        adb_home()
        start_time = time.time()
        # 使用枚举获取应用配置
        app_name = TVApps.get_app_by_manufacturer(TVApps.MANGO)
        if not app_name:
            print("当前厂商不支持芒果TV应用")
            return False
        print(f"启动芒果TV: {app_name}")
        start_app(app_name)
        package = get_front_package()
        # assert app_name.split("/")[0] in package  # 判断是否启动了该App
        if d(textContains="同意").exists():
            adb_center()
            time.sleep(2)
        adb_center()  # 跳过开机广告1
        time.sleep(1)
        adb_down()
        time.sleep(1)
        adb_back()  # 跳过广告2
        if "com.hunantv.license/com.mgtv.tv.channel.activity.VipMessageActivity" in package:
            adb_back()
        while time.time() - start_time < 300:
            if d(text='首页').exists():
                d(text='首页').click()
                adb_down()
                time.sleep(1)
                adb_right()
                time.sleep(1)
                adb_center()
                time.sleep(1)
                if d(text='全屏').exists():
                    d(text='全屏').click()
                    print('播放视频')
                    time.sleep(2)
                    return True
                else:
                    adb_back()
            else:
                adb_back()
        print('播放芒果失败，请重试')
        return False

    @ui_safe
    def test_KuaiTv(self):
        # 云视听快TV
        adb_home()
        start_time = time.time()
        # 使用枚举获取应用配置
        app_name = TVApps.get_app_by_manufacturer(TVApps.KUAISHOU)
        if not app_name:
            print("当前厂商不支持快手应用")
            return False
        print(f"启动快手快TV: {app_name}")
        start_app(app_name)
        package = get_front_package()
        # assert app_name.split("/")[0] in package  # 判断是否启动了该App
        print(package)
        if d(textContains="同意并继续").exists():
            adb_center()
        time.sleep(5)
        while time.time() - start_time < 300:
            if d(text='推荐').exists():
                d(text='推荐').click()
                adb_down()
                time.sleep(1)
                adb_center()
                print('播放视频')
                return True
            else:
                adb_back()
        print('播放快手失败，请重试')
        return False

    def test_localvideo(self,count=0):
        back2home_page()
        # 使用枚举获取应用配置
        app_name = TVApps.get_app_by_manufacturer(TVApps.MEDIA_EXPLORER)
        if app_name:
            start_app(app_name)
        elif is_huawei_tv():
            print("华为不能通过start_app的方式启动")
        else:
            print("当前厂商不支持媒体浏览器应用")
            return False
        find_path = False
        time.sleep(3)
        disk_name = str(timeout_command("adb shell ls /storage")).splitlines()
        disk_path = '/storage/' + disk_name[0]
        video_file_path = disk_path + '/1A视频播放压测码流'
        video_format_files = str(timeout_command("adb shell ls {}".format(video_file_path))).splitlines()
        video_num = len(video_format_files)
        print("当前本地视频文件数量为：" + str(video_num))
        if is_tcl_tv():
            [adb_up() for i in range(5)]
            adb_down()
            time.sleep(1)
            adb_down()
            time.sleep(1)
            adb_center()
            time.sleep(1)
            adb_center()
            time.sleep(1)
            for i in range(5):
                if d(text=u'1A视频播放压测码流').click_exist(timeout=10.0):
                    find_path = True
                    print('tcl find folder')
                    break
                else:
                    print(f'第{i+1}次未找到文件夹，继续查找')
                    [adb_quickdown() for i in range(9)]
            if not find_path:
                print('tcl 未找到视频文件夹')
                return False
            time.sleep(1)
            print(f'tcl检测到{video_num}个视频文件')
            if video_num > 0:
                # 根据实际视频数量随机选择
                select_video = random.randrange(video_num)
                print(f'随机选择第{select_video + 1}个视频: {video_format_files[select_video]}')

                # TCL界面是网格布局，一行最多5个文件
                # 计算目标文件的行列位置
                target_row = select_video // 5  # 第几行（从0开始）
                target_col = select_video % 5   # 第几列（从0开始）
                print(f'目标位置: 第{target_row + 1}行，第{target_col + 1}列')

                # 先向下移动到目标行
                for i in range(target_row):
                    adb_down()
                    time.sleep(1)

                # 再向右移动到目标列
                for i in range(target_col):
                    adb_right()
                    time.sleep(1)
                    # 选择并播放视频
                adb_center()
                time.sleep(2)
                print('播放tcl本地视频')
                return True
            else:
                print('未检测到视频文件')
                return False

        if is_hisense_tv():
            adb_center()
            time.sleep(1)
            adb_down()
            time.sleep(1)
            adb_center()
            if video_num > 0:
                select_video = random.randrange(video_num)
                print(f'播放视频: {video_format_files[select_video]}')
                [adb_down() for i in range(select_video+1)]
            adb_center()
            print('播放hisense本地视频')
            return True
        if is_huawei_tv():
            adb_home()
            time.sleep(1)
            adb_down()
            time.sleep(1)
            adb_down()
            time.sleep(1)
            adb_center()
            [adb_up() for i in range(5)]
            [adb_left() for i in range(5)]
            adb_down()
            time.sleep(1)
            adb_right()
            time.sleep(1)
            adb_right()
            time.sleep(1)
            adb_center()
            time.sleep(1)
            [adb_left() for i in range(5)]
            adb_right()
            time.sleep(1)
            adb_down()
            time.sleep(1)
            adb_center()
            time.sleep(1)
            if video_num > 0:
                select_video = random.randrange(video_num)
                print(f'播放视频: {video_format_files[select_video]}')
                [adb_down() for i in range(select_video+1)]
            adb_center()
            print('播放huawei本地视频')
            return True
        if is_xiaomi_tv():
            # 小米设备的原有逻辑
            assert d(resourceId='com.xiaomi.mitv.mediaexplorer:id/dev', text='设备').wait.exists(
                timeout=20000), 'launch Media Explorer failed!'
            d(resourceId='com.xiaomi.mitv.mediaexplorer:id/dev', text='设备').click.wait(timeout=3)
            d(resourceId="com.xiaomi.mitv.mediaexplorer:id/devices_logo").click.wait(timeout=3)
            disk_name = str(timeout_command("adb shell ls /storage")).splitlines()
            disk_path = '/storage/' + disk_name[0]
            video_file_path = None
            for i in range(5):
                try:
                    assert d(text=u'1A视频播放压测码流').wait.exists(
                        timeout=10000), 'can not find folder'
                    d(text=u'1A视频播放压测码流').click()
                    video_file_path = disk_path + '/1A视频播放压测码流'
                    print('find folder')
                    break
                except Exception as exp:
                    print(str(exp))
                    [adb_quickdown() for i in range(9)]
            time.sleep(5)
            if d(resourceId='com.xiaomi.mitv.mediaexplorer:id/no_file_layout', text='未发现可支持的视频、音乐、图片、文档文件').wait.exists(timeout=500):
                adb_back()
            else:
                video_format_files = str(timeout_command("adb shell ls {}".format(video_file_path))).splitlines()
                video_num = len(video_format_files)
                select_video = random.randrange(video_num)
                print(f'播放视频: {video_format_files[select_video]}')
                [adb_down() for i in range(select_video+1)]
                adb_center()
            return True

        return False


    def random_action(self, stop=lambda: False):
        while not stop():
            time.sleep(random.randrange(10))
            action = random.randint(1, 3)
            if action == 1:
                [adb_left() for i in range(3)]
                [adb_right() for i in range(3)]
                print('seek')
            elif action == 2:
                call_xiaoai()
                print('唤醒小爱')
            elif action == 3:
                adb_long_pressmenu()
                print('打开智能助手')
            time.sleep(5)

    def test_interaction(self):
        stop_action = False
        tvqs_screeencap(screenshot_2=3, interation=1, delay_start=0, interval_time=0,
                        rename_keyword="startVideo_screencap", screen_count=3)
        t = threading.Thread(target=self.random_action, args=(lambda: stop_action,))
        t.start()
        tvqs_screeencap(screenshot_2=3, interation=3, delay_start=1, interval_time=7,
                        rename_keyword="Video_screencap")
        if t.is_alive():
            stop_action = True
            t.join()

    def screenshot(self):
        tvqs_screeencap(screenshot_2=3, interation=1, delay_start=0, interval_time=0,
                        rename_keyword="startVideo_screencap", screen_count=3)
        tvqs_screeencap(screenshot_2=3, interation=3, delay_start=1, interval_time=7,
                        rename_keyword="Video_screencap")

    def test_randomplayer(self):
        version = timeout_command('adb shell getprop ro.vendor.build.version.incremental')
        case_id = random.randrange(9)
        play_result = False
        app_type = None  # 记录应用类型用于退出
        str_success = self.enter_exit_str()
        if not str_success:
            print("亮屏失败，跳过用例")
            return
        # case_id = 8
        if case_id == 0:
            print("=================================================\n")
            print("           银河奇异果\n")
            print("=================================================\n")
            play_result = self.testAiQiYi()
            app_type = 'iqiyi'
        elif case_id == 1:
            print("=================================================\n")
            print("           云视听极光\n")
            print("=================================================\n")
            play_result = self.testYunshiting()
            app_type = 'tencent'
        elif case_id == 2:
            print("=================================================\n")
            print("           CIBN酷喵\n")
            print("=================================================\n")
            play_result = self.testYouku()
            app_type = 'youku'
        elif case_id == 3:
            print("=================================================\n")
            print("           芒果TV\n")
            print("=================================================\n")
            play_result = self.test_Mangotv()
            app_type = 'mango'
        elif case_id == 4:
            print("=================================================\n")
            print("           云视听小电视\n")
            print("=================================================\n")
            play_result = self.testBilibili()
            app_type = 'bilibili'
        elif case_id == 5:
            print("=================================================\n")
            print("           云视听快TV\n")
            print("=================================================\n")
            play_result = self.test_KuaiTv()
            app_type = 'kuaishou'
        elif case_id == 6:
            print("=================================================\n")
            print("           高清播放器(U盘)\n")
            print("=================================================\n")
            play_result = self.test_localvideo()
            app_type = 'localvideo'
        elif case_id == 7:
            print("=================================================\n")
            print("           在线视频\n")
            print("=================================================\n")
            play_result = self.testOnline_video()
            app_type = 'online'
        elif case_id == 8:
            print("=================================================\n")
            print("           手机DLNA投屏测试\n")
            print("=================================================\n")
            play_result = self.test_dlna_tencent_casting()
            app_type = 'dlna_casting'

        if play_result:
            self.test_interaction()
            # 添加应用退出逻辑
            if app_type:
                self.exit_app_playback(app_type)

            str_success = self.enter_exit_str()
            if not str_success:
                print("case结束str失败")
                return
        else:
            print("case执行失败")
        if self.dlna_handle:
            try:
                self.dlna_handle.reset_tv()
                print("DLNA资源清理完成")
            except:
                print("DLNA资源清理失败")
                pass
        return

    def test_play_and_seek(self):
        version = timeout_command('adb shell getprop ro.vendor.build.version.incremental')
        case_id = random.randrange(8)
        if case_id == 0:
            print("=================================================\n")
            print("           银河奇异果\n")
            print("=================================================\n")
            if self.testAiQiYi():
                self.screenshot()
                print("快进")
                for i in range(5):
                    adb_right()
                time.sleep(5)
                print("快退")
                for i in range(5):
                    adb_left()
                time.sleep(5)
                tvqs_screeencap(screenshot_2=3, interation=3, delay_start=0, interval_time=7,
                                rename_keyword="after_seek_screencap")
                print('退出播放')
                [adb_back() for i in range(4)]
                adb_center()
        elif case_id == 1:
            print("=================================================\n")
            print("           云视听极光\n")
            print("=================================================\n")
            if self.testYunshiting():
                self.screenshot()
                print("快进")
                for r in range(4):  # 快进
                    adb_right()
                time.sleep(2)
                print("快退")
                for l in range(4):  # 快退
                    adb_left()
                tvqs_screeencap(screenshot_2=3, interation=3, delay_start=0, interval_time=7,
                                rename_keyword="after_seek_screencap")
                print('退出播放')
                [adb_back() for i in range(4)]
                adb_center()
        elif case_id == 2:
            print("=================================================\n")
            print("           CIBN酷喵\n")
            print("=================================================\n")
            if self.testYouku():
                self.screenshot()
                print("快进")
                for i in range(5):
                    adb_right()
                time.sleep(5)
                print("快退")
                for i in range(5):
                    adb_left()
                tvqs_screeencap(screenshot_2=3, interation=3, delay_start=0, interval_time=7,
                                rename_keyword="after_seek_screencap")
                print('退出播放')
                [adb_back() for i in range(4)]
                adb_center()
        elif case_id == 3:
            print("=================================================\n")
            print("           芒果TV\n")
            print("=================================================\n")
            if self.test_Mangotv():
                self.screenshot()
                print("快进")
                for r in range(5):  # 快进
                    adb_right()
                time.sleep(2)
                print("快退")
                for l in range(5):  # 快退
                    adb_left()
                tvqs_screeencap(screenshot_2=3, interation=3, delay_start=0, interval_time=7,
                                rename_keyword="after_seek_screencap")
                print('退出播放')
                [adb_back() for i in range(4)]
                adb_center()
        elif case_id == 4:
            print("=================================================\n")
            print("           云视听小电视\n")
            print("=================================================\n")
            if self.testBilibili():
                self.screenshot()
                print("快进")
                for i in range(5):
                    adb_right()
                adb_center()
                time.sleep(5)
                print("快退")
                for i in range(5):
                    adb_left()
                adb_center()
                tvqs_screeencap(screenshot_2=3, interation=3, delay_start=0, interval_time=7,
                                rename_keyword="after_seek_screencap")
                print('退出播放')
                [adb_back() for i in range(3)]
                adb_center()
        elif case_id == 5:
            print("=================================================\n")
            print("           云视听快TV\n")
            print("=================================================\n")
            if self.test_KuaiTv():
                self.screenshot()
                print("快进")
                for r in range(5):  # 快进
                    adb_right()
                time.sleep(2)
                print("快退")
                for l in range(5):  # 快退
                    adb_left()
                tvqs_screeencap(screenshot_2=3, interation=3, delay_start=0, interval_time=7,
                                rename_keyword="after_seek_screencap")
                print('退出播放')
                [adb_back() for i in range(3)]
                adb_center()
        elif case_id == 6:
            print("=================================================\n")
            print("           高清播放器(U盘)\n")
            print("=================================================\n")
            if self.test_localvideo():
                self.screenshot()
                print("快进")
                for r in range(5):  # 快进
                    adb_right()
                time.sleep(2)
                print("快退")
                for l in range(5):  # 快退
                    adb_left()
                tvqs_screeencap(screenshot_2=3, interation=3, delay_start=0, interval_time=7,
                                rename_keyword="after_seek_screencap")
                time.sleep(5)
                print('退出播放')
                [adb_back() for i in range(6)]
        elif case_id == 7:
            play_result = False
            print("=================================================\n")
            print("           在线视频\n")
            print("=================================================\n")
            play_result = self.testOnline_video()
        if play_result:
            self.screenshot()
            print("快进")
            for r in range(4):  # 快进
                adb_right()
            adb_center()
            time.sleep(2)
            print("快退")
            for l in range(4):  # 快退
                adb_left()
            adb_center()
            tvqs_screeencap(screenshot_2=3, interation=3, delay_start=0, interval_time=7,
                            rename_keyword="after_seek_screencap")
            print('退出播放')
            adb_back()
            time.sleep(2)
            adb_back()

    def test_localvideo_interaction(self,count=0):
        back2home_page()
        # 使用枚举获取应用配置
        app_name = TVApps.get_app_by_manufacturer(TVApps.MEDIA_EXPLORER)
        if app_name:
            start_app(app_name)
        else:
            print("当前厂商不支持媒体浏览器应用")
            return False
        find_path = False
        time.sleep(3)
        disk_name = str(timeout_command("adb shell ls /storage")).splitlines()
        disk_path = '/storage/' + disk_name[0]
        video_file_path = disk_path + '/1A视频播放压测码流'
        video_format_files = str(timeout_command("adb shell ls {}".format(video_file_path))).splitlines()
        video_num = len(video_format_files)
        print("当前本地视频文件数量为：" + str(video_num))

        if is_xiaomi_tv():
            # 小米设备的原有逻辑
            if d(resourceId='com.xiaomi.mitv.mediaexplorer:id/dev', text='设备').click_exists(timeout=20.0):
                time.sleep(3)
                d(resourceId="com.xiaomi.mitv.mediaexplorer:id/devices_logo").click_exists()
                time.sleep(3)
            else:
                print('未找到设备按钮，使用ADB导航')
                adb_down()
                time.sleep(2)
                adb_center()
                time.sleep(3)

            for i in range(5):
                if d(text=u'1A视频播放压测码流').click_exist(timeout=10.0):
                    find_path = True
                    print('find folder')
                    break
                else:
                    print(f'第{i+1}次未找到文件夹，继续查找')
                    [adb_quickdown() for i in range(9)]
            # 不需要重复检测是否存在u盘文件, 加快运行效率
            if not find_path:
                print('未找到视频文件夹')
                return False
            time.sleep(5)
            if d(resourceId='com.xiaomi.mitv.mediaexplorer:id/no_file_layout', text='未发现可支持的视频、音乐、图片、文档文件').click_exists(timeout=5):
                adb_back()
                print('无可播放文件')
                return False
            else:
                select_video = random.randrange(video_num)
                print(f'播放视频: {video_format_files[select_video]}')
                [adb_down() for i in range(select_video+1)]
                adb_center()
                print('小米播放视频')

            adb_center()
            time.sleep(1)
            adb_center()
            time.sleep(1)
            adb_left()
            # 调用通用的视频交互操作
            return self.video_interaction_operations()
        elif is_hisense_tv():
            adb_center()
            time.sleep(1)
            adb_down()
            time.sleep(1)
            adb_center()
            if video_num > 0:
                select_video = random.randrange(video_num)
                print(f'播放视频: {video_format_files[select_video]}')
                [adb_down() for i in range(select_video+1)]
            adb_center()
            print('播放hisense本地视频')
            # 调用通用的视频交互操作
            return self.video_interaction_operations()
        elif is_tcl_tv:
            adb_right()
            time.sleep(2)
            adb_center()
            time.sleep(10)
            adb_down()
            time.sleep(2)

            # 获取当前目录下的视频文件数量
            try:
                # 尝试获取当前目录的视频文件列表
                print(f'tcl检测到{video_num}个视频文件')
                if video_num > 0:
                    # 根据实际视频数量随机选择
                    select_video = random.randrange(video_num)
                    print(f'随机选择第{select_video + 1}个视频: {video_format_files[select_video]}')

                    # TCL界面是网格布局，一行最多5个文件
                    # 计算目标文件的行列位置
                    target_row = select_video // 5  # 第几行（从0开始）
                    target_col = select_video % 5   # 第几列（从0开始）
                    print(f'目标位置: 第{target_row + 1}行，第{target_col + 1}列')

                    # 先向下移动到目标行
                    for i in range(target_row):
                        adb_down()
                        time.sleep(1)

                    # 再向右移动到目标列
                    for i in range(target_col):
                        adb_right()
                        time.sleep(1)
                else:
                    print('未检测到视频文件，使用默认选择')
            except Exception as e:
                print(f'获取tcl视频文件列表时出错: {e}')
                return
            # 选择并播放视频
            adb_center()
            time.sleep(2)
            print('播放tcl本地视频')
            return self.video_interaction_operations()
        elif is_huawei_tv():
            [adb_up() for i in range(5)]
            [adb_left() for i in range(5)]
            adb_down()
            time.sleep(1)
            adb_right()
            time.sleep(1)
            adb_right()
            time.sleep(1)
            adb_center()
            time.sleep(1)
            [adb_left() for i in range(5)]
            adb_right()
            time.sleep(1)
            adb_down()
            time.sleep(1)
            adb_center()
            time.sleep(1)
            if video_num > 0:
                select_video = random.randrange(video_num)
                print(f'播放视频: {video_format_files[select_video]}')
                [adb_down() for i in range(select_video+1)]
            adb_center()
            print('播放huawei本地视频')
            return self.video_interaction_operations()
        else:
            return False

def click_by_image(template_name):
    """
    通过图片匹配点击元素
    :param template_name: 模板图片名称
    """
    b_success = False
    capture_path = "/sdcard/screencaps"
    captures = str(timeout_command("adb shell ls -t {} | grep -E 'playcheck'".format(capture_path))).splitlines()
    playtest = captures[0]
    print(playtest)
    playcheck = f'{capture_path}/{playtest}'
    current_dir = os.path.dirname(os.path.abspath(__file__))
    script_dir = os.path.dirname(current_dir)
    template_dir = f"{script_dir}/pics/playerapp.MiplayerTest.testplay"
    # 推送截屏
    timeout_command(f'adb pull {playcheck} {template_dir}')
    playtest_img = f"{template_dir}/{playtest}"
    #获取屏幕截图,返回OpenCV格式的numpy数组
    screenshot = cv2.imread(playtest_img)
    for template_pic in os.listdir(template_dir):
        if template_pic.startswith(template_name):
            template_path = f"{template_dir}/{template_pic}"
            #读取模板图片
            template = cv2.imread(template_path)
            #模板匹配
            res = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(res)
            if os.path.isfile(playtest_img):
                os.remove(playtest_img)
            if max_val < 0.90:
                print(f"未找到匹配项（最高相似度: {max_val:.2f} < 阈值 0.90）")
                return False
            #计算点击位置（模板中心点）
            h, w = template.shape[:2]
            x, y = max_loc[0] + w // 2, max_loc[1] + h // 2
            d.click(x, y)
            print("选中控件")
            time.sleep(1)
            #第一次选中，第二次点击，其他情况需要适配
            d.click(x, y)
            print("成功点击图片")
            b_success = True
            break
    if b_success:
        return True
    print(f"模板图片不存在: {template_name}")
    return False
