#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 atvsource.py 文件的导入和基本功能
"""

import sys
import os

# 添加项目路径到 sys.path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'script', 'testcases'))

try:
    # 尝试导入修改后的模块
    from atvsource import ATVSource
    print("✓ 成功导入 ATVSource 类")
    
    # 检查是否使用了 u2
    import uiautomator2 as u2
    print("✓ uiautomator2 模块可用")
    
    # 尝试创建实例（不运行测试）
    print("✓ atvsource.py 已成功从 uiautomator 迁移到 u2")
    print("主要变更:")
    print("  - 导入: from uiautomator import device as d -> import uiautomator2 as u2")
    print("  - 设备连接: 在 setUp() 中添加 self.d = u2.connect()")
    print("  - UI操作: d(...) -> self.d(...)")
    print("  - 按键操作: d.press.home() -> self.d.press('home')")
    print("  - 等待操作: .wait.exists(timeout=20000) -> .wait(timeout=20)")
    print("  - 点击操作: .click.wait(timeout=300) -> .click(timeout=0.3)")
    print("  - Watcher操作: d.watchers.remove() -> self.d.watcher.remove()")
    
except ImportError as e:
    print(f"✗ 导入失败: {e}")
    sys.exit(1)
except Exception as e:
    print(f"✗ 其他错误: {e}")
    sys.exit(1)
