#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实际执行 sendevent 长按按键的脚本
可以真正执行你提供的事件序列
"""

import time
import subprocess
import sys

def execute_adb_command(cmd, description, timeout=10):
    """执行adb命令"""
    print(f"🔧 {description}")
    print(f"   命令: {cmd}")
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout)
        if result.returncode == 0:
            print(f"   ✅ 执行成功")
            if result.stdout.strip():
                print(f"   输出: {result.stdout.strip()}")
        else:
            print(f"   ❌ 执行失败 (返回码: {result.returncode})")
            if result.stderr.strip():
                print(f"   错误: {result.stderr.strip()}")
            return False
    except subprocess.TimeoutExpired:
        print(f"   ⏰ 命令超时")
        return False
    except Exception as e:
        print(f"   ❌ 异常: {e}")
        return False
    
    print()
    return True

def check_adb_connection():
    """检查ADB连接"""
    print("🔍 检查ADB设备连接...")
    result = subprocess.run("adb devices", shell=True, capture_output=True, text=True)
    
    if result.returncode != 0:
        print("❌ ADB命令执行失败")
        return False
    
    lines = result.stdout.strip().split('\n')
    devices = [line for line in lines[1:] if line.strip() and 'device' in line and 'offline' not in line]
    
    if not devices:
        print("❌ 未发现连接的设备")
        print("请确保:")
        print("1. 设备已连接并开启USB调试")
        print("2. 已授权ADB调试")
        print("3. 运行 'adb devices' 确认设备状态")
        return False
    
    print(f"✅ 发现 {len(devices)} 个设备:")
    for device in devices:
        print(f"   📱 {device}")
    return True

def execute_longpress_sendevent(device_path='/dev/input/event7', key_code=139, duration_seconds=2):
    """
    实际执行sendevent长按按键
    
    Args:
        device_path: 输入设备路径 (默认 /dev/input/event7)
        key_code: 按键扫描码 (默认 139 = 0x8b = 菜单键)
        duration_seconds: 长按时长 (默认 2秒)
    """
    print("🎯 开始执行 sendevent 长按按键")
    print("=" * 50)
    print(f"设备路径: {device_path}")
    print(f"按键码: {key_code} (0x{key_code:02x})")
    print(f"长按时长: {duration_seconds} 秒")
    print("=" * 50)
    print()
    
    # 步骤1: 按下按键
    print("📍 步骤1: 按下按键")
    print("   对应事件: /dev/input/event7: 0001 008b 00000001")
    cmd1 = f'adb shell sendevent {device_path} 1 {key_code} 1'
    if not execute_adb_command(cmd1, "发送按键按下事件"):
        return False
    
    # 步骤2: 同步事件
    print("📍 步骤2: 发送同步事件")
    print("   对应事件: /dev/input/event7: 0000 0000 00000000")
    cmd2 = f'adb shell sendevent {device_path} 0 0 0'
    if not execute_adb_command(cmd2, "发送同步事件"):
        return False
    
    # 步骤3: 保持按下状态
    print(f"📍 步骤3: 保持按下状态 {duration_seconds} 秒")
    for i in range(duration_seconds):
        print(f"   ⏳ 长按中... {i+1}/{duration_seconds} 秒")
        time.sleep(1)
    print("   ✅ 长按时间结束")
    print()
    
    # 步骤4: 释放按键
    print("📍 步骤4: 释放按键")
    print("   对应事件: /dev/input/event7: 0001 008b 00000000")
    cmd3 = f'adb shell sendevent {device_path} 1 {key_code} 0'
    if not execute_adb_command(cmd3, "发送按键释放事件"):
        return False
    
    # 步骤5: 最终同步事件
    print("📍 步骤5: 发送最终同步事件")
    print("   对应事件: /dev/input/event7: 0000 0000 00000000")
    cmd4 = f'adb shell sendevent {device_path} 0 0 0'
    if not execute_adb_command(cmd4, "发送最终同步事件"):
        return False
    
    print("🎉 长按按键执行完成!")
    print("=" * 50)
    return True

def interactive_execution():
    """交互式执行"""
    print("\n🎮 交互式执行模式")
    print("=" * 30)
    
    # 获取参数
    try:
        duration = int(input("请输入长按时长(秒，默认2): ") or "2")
        if duration <= 0:
            duration = 2
    except ValueError:
        duration = 2
    
    device_path = input("请输入设备路径(默认/dev/input/event7): ").strip() or "/dev/input/event7"
    
    try:
        key_code = int(input("请输入按键码(默认139=菜单键): ") or "139")
        if key_code <= 0:
            key_code = 139
    except ValueError:
        key_code = 139
    
    print(f"\n📋 执行参数:")
    print(f"   设备路径: {device_path}")
    print(f"   按键码: {key_code}")
    print(f"   长按时长: {duration} 秒")
    
    confirm = input("\n确认执行? (y/N): ").lower().strip()
    if confirm != 'y':
        print("❌ 取消执行")
        return
    
    print()
    execute_longpress_sendevent(device_path, key_code, duration)

def quick_test():
    """快速测试 - 长按菜单键2秒"""
    print("\n⚡ 快速测试: 长按菜单键2秒")
    print("=" * 35)
    
    confirm = input("确认执行长按菜单键2秒? (y/N): ").lower().strip()
    if confirm != 'y':
        print("❌ 取消执行")
        return
    
    print()
    execute_longpress_sendevent('/dev/input/event7', 139, 2)

def show_help():
    """显示帮助信息"""
    print("\n📖 帮助信息")
    print("=" * 20)
    print("此脚本用于执行基于sendevent的长按按键操作")
    print()
    print("🔧 使用前准备:")
    print("1. 确保设备已连接: adb devices")
    print("2. 确保设备已开启USB调试")
    print("3. 某些设备可能需要root权限")
    print()
    print("📱 常用按键码:")
    print("   139 (0x8b) - 菜单键")
    print("   116 (0x74) - 电源键")
    print("   158 (0x9e) - 返回键")
    print("   172 (0xac) - 主页键")
    print()
    print("🗂️  常用设备路径:")
    print("   /dev/input/event0 到 /dev/input/event9")
    print("   可通过 'adb shell ls /dev/input/' 查看")
    print()
    print("⚠️  注意事项:")
    print("- 不同设备的输入设备路径可能不同")
    print("- 扫描码可能因设备厂商而异")
    print("- 建议先在测试设备上验证")

def main():
    """主函数"""
    print("🚀 sendevent 长按按键执行脚本")
    print("基于你提供的事件序列实际执行")
    
    # 检查ADB连接
    if not check_adb_connection():
        print("\n⚠️  设备连接检查失败，但仍可继续使用脚本")
        print("如果设备实际已连接，可能是权限或配置问题")
    
    while True:
        print("\n📋 选择操作:")
        print("1. 快速测试 (长按菜单键2秒)")
        print("2. 交互式执行")
        print("3. 显示帮助")
        print("4. 退出")
        
        choice = input("\n请选择 (1-4): ").strip()
        
        if choice == '1':
            quick_test()
        elif choice == '2':
            interactive_execution()
        elif choice == '3':
            show_help()
        elif choice == '4':
            print("👋 再见!")
            break
        else:
            print("❌ 无效选择，请重试")

if __name__ == "__main__":
    main()
